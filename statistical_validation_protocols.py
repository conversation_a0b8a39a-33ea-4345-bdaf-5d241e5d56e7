"""
Statistical Validation Protocols for RL vs CHT Research Project

Comprehensive statistical testing framework ensuring rigorous experimental validation
with proper significance testing, confidence intervals, effect size measurements,
and multiple comparison corrections for academic publication standards.

Implements best practices for:
- Hypothesis testing with appropriate statistical tests
- Multiple comparison corrections (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FDR)
- Effect size calculations (<PERSON>'s d, eta-squared)
- Confidence interval estimation
- Power analysis and sample size determination
- Non-parametric alternatives for robustness

Authors: <AUTHORS>
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import scipy.stats as stats
from scipy.stats import ttest_ind, mannwhitneyu, wilcoxon, kruskal, friedmanchisquare
from statsmodels.stats.multitest import multipletests
from statsmodels.stats.power import ttest_power
from statsmodels.stats.contingency_tables import mcnemar
import warnings
import logging

class StatisticalTest(Enum):
    """Types of statistical tests"""
    T_TEST_INDEPENDENT = "t_test_independent"
    T_TEST_PAIRED = "t_test_paired"
    MANN_WHITNEY_U = "mann_whitney_u"
    WILCOXON_SIGNED_RANK = "wilcoxon_signed_rank"
    KRUSKAL_WALLIS = "kruskal_wallis"
    FRIEDMAN = "friedman"
    CHI_SQUARE = "chi_square"
    MCNEMAR = "mcnemar"

class MultipleComparisonMethod(Enum):
    """Multiple comparison correction methods"""
    BONFERRONI = "bonferroni"
    HOLM_BONFERRONI = "holm"
    BENJAMINI_HOCHBERG = "fdr_bh"
    BENJAMINI_YEKUTIELI = "fdr_by"
    SIDAK = "sidak"

class EffectSizeMethod(Enum):
    """Effect size calculation methods"""
    COHENS_D = "cohens_d"
    HEDGES_G = "hedges_g"
    GLASS_DELTA = "glass_delta"
    ETA_SQUARED = "eta_squared"
    OMEGA_SQUARED = "omega_squared"
    CLIFF_DELTA = "cliff_delta"

@dataclass
class StatisticalTestResult:
    """Results from a statistical test"""
    test_name: str
    test_statistic: float
    p_value: float
    degrees_of_freedom: Optional[int]
    critical_value: Optional[float]
    confidence_interval: Optional[Tuple[float, float]]
    effect_size: Optional[float]
    effect_size_interpretation: str
    power: Optional[float]
    sample_sizes: Tuple[int, ...]
    assumptions_met: Dict[str, bool]
    interpretation: str

@dataclass
class MultipleComparisonResult:
    """Results from multiple comparison correction"""
    original_p_values: np.ndarray
    corrected_p_values: np.ndarray
    rejected_hypotheses: np.ndarray
    method: MultipleComparisonMethod
    alpha_level: float
    family_wise_error_rate: float

@dataclass
class ValidationProtocol:
    """Complete validation protocol specification"""
    protocol_name: str
    primary_hypothesis: str
    secondary_hypotheses: List[str]
    alpha_level: float
    power_requirement: float
    effect_size_threshold: float
    statistical_tests: List[StatisticalTest]
    multiple_comparison_method: MultipleComparisonMethod
    confidence_level: float
    sample_size_per_group: int
    number_of_comparisons: int

class StatisticalValidator:
    """Main class for statistical validation"""
    
    def __init__(self, alpha: float = 0.05, power: float = 0.8):
        self.alpha = alpha
        self.power = power
        self.logger = logging.getLogger(__name__)
        
    def perform_t_test(self, group1: np.ndarray, group2: np.ndarray, 
                      paired: bool = False, alternative: str = 'two-sided') -> StatisticalTestResult:
        """Perform t-test with comprehensive validation"""
        
        # Check assumptions
        assumptions = self._check_t_test_assumptions(group1, group2, paired)
        
        # Perform test
        if paired:
            statistic, p_value = stats.ttest_rel(group1, group2, alternative=alternative)
            df = len(group1) - 1
            test_name = "Paired t-test"
        else:
            statistic, p_value = stats.ttest_ind(group1, group2, alternative=alternative)
            df = len(group1) + len(group2) - 2
            test_name = "Independent t-test"
        
        # Calculate effect size
        effect_size = self._calculate_cohens_d(group1, group2, paired)
        effect_interpretation = self._interpret_cohens_d(effect_size)
        
        # Calculate confidence interval
        if paired:
            diff = group1 - group2
            ci = stats.t.interval(1 - self.alpha, df, np.mean(diff), stats.sem(diff))
        else:
            pooled_se = np.sqrt((np.var(group1, ddof=1) / len(group1)) + 
                               (np.var(group2, ddof=1) / len(group2)))
            mean_diff = np.mean(group1) - np.mean(group2)
            ci = stats.t.interval(1 - self.alpha, df, mean_diff, pooled_se)
        
        # Calculate power
        power = self._calculate_t_test_power(group1, group2, paired)
        
        # Critical value
        critical_value = stats.t.ppf(1 - self.alpha/2, df)
        
        return StatisticalTestResult(
            test_name=test_name,
            test_statistic=statistic,
            p_value=p_value,
            degrees_of_freedom=df,
            critical_value=critical_value,
            confidence_interval=ci,
            effect_size=effect_size,
            effect_size_interpretation=effect_interpretation,
            power=power,
            sample_sizes=(len(group1), len(group2)),
            assumptions_met=assumptions,
            interpretation=self._interpret_test_result(p_value, effect_size, power)
        )
    
    def perform_mann_whitney_u(self, group1: np.ndarray, group2: np.ndarray, 
                              alternative: str = 'two-sided') -> StatisticalTestResult:
        """Perform Mann-Whitney U test (non-parametric alternative to t-test)"""
        
        statistic, p_value = stats.mannwhitneyu(group1, group2, alternative=alternative)
        
        # Calculate effect size (Cliff's delta)
        effect_size = self._calculate_cliff_delta(group1, group2)
        effect_interpretation = self._interpret_cliff_delta(effect_size)
        
        # No assumptions to check for non-parametric test
        assumptions = {"non_parametric": True}
        
        return StatisticalTestResult(
            test_name="Mann-Whitney U test",
            test_statistic=statistic,
            p_value=p_value,
            degrees_of_freedom=None,
            critical_value=None,
            confidence_interval=None,
            effect_size=effect_size,
            effect_size_interpretation=effect_interpretation,
            power=None,  # Power calculation complex for non-parametric tests
            sample_sizes=(len(group1), len(group2)),
            assumptions_met=assumptions,
            interpretation=self._interpret_test_result(p_value, effect_size, None)
        )
    
    def perform_multiple_comparisons(self, p_values: np.ndarray, 
                                   method: MultipleComparisonMethod = MultipleComparisonMethod.HOLM_BONFERRONI,
                                   alpha: float = None) -> MultipleComparisonResult:
        """Perform multiple comparison correction"""
        
        if alpha is None:
            alpha = self.alpha
        
        # Apply correction
        rejected, corrected_p_values, alpha_sidak, alpha_bonf = multipletests(
            p_values, alpha=alpha, method=method.value
        )
        
        # Calculate family-wise error rate
        if method == MultipleComparisonMethod.BONFERRONI:
            fwer = min(1.0, len(p_values) * alpha)
        elif method == MultipleComparisonMethod.SIDAK:
            fwer = 1 - (1 - alpha) ** len(p_values)
        else:
            fwer = alpha  # For FDR methods, this is the expected proportion of false discoveries
        
        return MultipleComparisonResult(
            original_p_values=p_values,
            corrected_p_values=corrected_p_values,
            rejected_hypotheses=rejected,
            method=method,
            alpha_level=alpha,
            family_wise_error_rate=fwer
        )
    
    def calculate_required_sample_size(self, effect_size: float, power: float = None,
                                     alpha: float = None, test_type: str = 'two_sample') -> int:
        """Calculate required sample size for desired power"""

        if power is None:
            power = self.power
        if alpha is None:
            alpha = self.alpha

        if test_type == 'two_sample':
            # For two-sample t-test - simplified calculation
            z_alpha = stats.norm.ppf(1 - alpha/2)
            z_beta = stats.norm.ppf(power)
            n = 2 * ((z_alpha + z_beta) / effect_size) ** 2
            return int(np.ceil(n))
        else:
            # Simplified calculation for other tests
            z_alpha = stats.norm.ppf(1 - alpha/2)
            z_beta = stats.norm.ppf(power)
            n = 2 * ((z_alpha + z_beta) / effect_size) ** 2
            return int(np.ceil(n))
    
    def _check_t_test_assumptions(self, group1: np.ndarray, group2: np.ndarray, 
                                 paired: bool) -> Dict[str, bool]:
        """Check assumptions for t-test"""
        assumptions = {}
        
        # Normality test (Shapiro-Wilk for small samples, Anderson-Darling for larger)
        if len(group1) <= 50:
            _, p1 = stats.shapiro(group1)
            _, p2 = stats.shapiro(group2)
        else:
            _, p1 = stats.normaltest(group1)
            _, p2 = stats.normaltest(group2)
        
        assumptions['normality_group1'] = p1 > 0.05
        assumptions['normality_group2'] = p2 > 0.05
        
        if not paired:
            # Equal variances test (Levene's test)
            _, p_var = stats.levene(group1, group2)
            assumptions['equal_variances'] = p_var > 0.05
        
        # Independence assumption (assumed to be met by design)
        assumptions['independence'] = True
        
        return assumptions
    
    def _calculate_cohens_d(self, group1: np.ndarray, group2: np.ndarray, paired: bool) -> float:
        """Calculate Cohen's d effect size"""
        if paired:
            diff = group1 - group2
            return np.mean(diff) / np.std(diff, ddof=1)
        else:
            pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) + 
                                 (len(group2) - 1) * np.var(group2, ddof=1)) / 
                                (len(group1) + len(group2) - 2))
            return (np.mean(group1) - np.mean(group2)) / pooled_std
    
    def _calculate_cliff_delta(self, group1: np.ndarray, group2: np.ndarray) -> float:
        """Calculate Cliff's delta effect size for non-parametric tests"""
        n1, n2 = len(group1), len(group2)
        
        # Count pairs where group1 > group2, group1 < group2
        greater = 0
        less = 0
        
        for x1 in group1:
            for x2 in group2:
                if x1 > x2:
                    greater += 1
                elif x1 < x2:
                    less += 1
        
        return (greater - less) / (n1 * n2)
    
    def _interpret_cohens_d(self, d: float) -> str:
        """Interpret Cohen's d effect size"""
        abs_d = abs(d)
        if abs_d < 0.2:
            return "negligible"
        elif abs_d < 0.5:
            return "small"
        elif abs_d < 0.8:
            return "medium"
        else:
            return "large"
    
    def _interpret_cliff_delta(self, delta: float) -> str:
        """Interpret Cliff's delta effect size"""
        abs_delta = abs(delta)
        if abs_delta < 0.147:
            return "negligible"
        elif abs_delta < 0.33:
            return "small"
        elif abs_delta < 0.474:
            return "medium"
        else:
            return "large"
    
    def _calculate_t_test_power(self, group1: np.ndarray, group2: np.ndarray, paired: bool) -> float:
        """Calculate observed power of t-test"""
        effect_size = abs(self._calculate_cohens_d(group1, group2, paired))
        n = len(group1) if paired else min(len(group1), len(group2))

        # Use correct parameter name for statsmodels ttest_power
        return ttest_power(effect_size, n, alpha=self.alpha)
    
    def _interpret_test_result(self, p_value: float, effect_size: float, power: Optional[float]) -> str:
        """Provide interpretation of test results"""
        interpretation = []
        
        if p_value < self.alpha:
            interpretation.append("Statistically significant difference detected")
        else:
            interpretation.append("No statistically significant difference detected")
        
        if effect_size is not None:
            if abs(effect_size) < 0.2:
                interpretation.append("with negligible practical significance")
            elif abs(effect_size) < 0.5:
                interpretation.append("with small practical significance")
            elif abs(effect_size) < 0.8:
                interpretation.append("with medium practical significance")
            else:
                interpretation.append("with large practical significance")
        
        if power is not None:
            if power < 0.8:
                interpretation.append(f"(Warning: Low statistical power = {power:.3f})")
        
        return " ".join(interpretation)

class ExperimentalValidationFramework:
    """Framework for comprehensive experimental validation"""
    
    def __init__(self):
        self.validator = StatisticalValidator()
        self.protocols: Dict[str, ValidationProtocol] = {}
        self.results: Dict[str, List[StatisticalTestResult]] = {}
        self._create_standard_protocols()
    
    def _create_standard_protocols(self):
        """Create standard validation protocols for RL vs CHT experiments"""
        
        # Protocol for Tier 1 validation (known optimal)
        tier1_protocol = ValidationProtocol(
            protocol_name="Tier1_Known_Optimal_Validation",
            primary_hypothesis="RL policy performance equals known optimal policy performance",
            secondary_hypotheses=[
                "RL convergence time is reasonable",
                "RL policy structure matches optimal structure"
            ],
            alpha_level=0.01,  # Stricter for known optimal
            power_requirement=0.9,
            effect_size_threshold=0.1,  # Small effect size acceptable
            statistical_tests=[StatisticalTest.T_TEST_PAIRED, StatisticalTest.WILCOXON_SIGNED_RANK],
            multiple_comparison_method=MultipleComparisonMethod.HOLM_BONFERRONI,
            confidence_level=0.99,
            sample_size_per_group=30,
            number_of_comparisons=3
        )
        
        # Protocol for Tier 2 validation (CHT comparison)
        tier2_protocol = ValidationProtocol(
            protocol_name="Tier2_CHT_Comparison",
            primary_hypothesis="RL policy performance is superior to CHT policy performance",
            secondary_hypotheses=[
                "RL shows better robustness to parameter uncertainty",
                "RL adapts better to changing conditions"
            ],
            alpha_level=0.05,
            power_requirement=0.8,
            effect_size_threshold=0.2,  # Medium effect size desired
            statistical_tests=[StatisticalTest.T_TEST_INDEPENDENT, StatisticalTest.MANN_WHITNEY_U],
            multiple_comparison_method=MultipleComparisonMethod.BENJAMINI_HOCHBERG,
            confidence_level=0.95,
            sample_size_per_group=50,
            number_of_comparisons=5
        )
        
        # Protocol for Tier 3 validation (backcast analysis)
        tier3_protocol = ValidationProtocol(
            protocol_name="Tier3_Backcast_Analysis",
            primary_hypothesis="RL policy achieves reasonable performance relative to backcast optimal",
            secondary_hypotheses=[
                "RL performance is within acceptable bounds of backcast optimal",
                "RL shows consistent performance across scenarios"
            ],
            alpha_level=0.05,
            power_requirement=0.8,
            effect_size_threshold=0.3,  # Larger effect size acceptable
            statistical_tests=[StatisticalTest.T_TEST_INDEPENDENT, StatisticalTest.KRUSKAL_WALLIS],
            multiple_comparison_method=MultipleComparisonMethod.BONFERRONI,
            confidence_level=0.95,
            sample_size_per_group=40,
            number_of_comparisons=4
        )
        
        self.protocols["tier1"] = tier1_protocol
        self.protocols["tier2"] = tier2_protocol
        self.protocols["tier3"] = tier3_protocol
    
    def validate_experiment(self, protocol_name: str, rl_results: np.ndarray, 
                          benchmark_results: np.ndarray, experiment_id: str) -> Dict[str, Any]:
        """Perform complete validation for an experiment"""
        
        if protocol_name not in self.protocols:
            raise ValueError(f"Protocol {protocol_name} not found")
        
        protocol = self.protocols[protocol_name]
        validation_results = {
            'experiment_id': experiment_id,
            'protocol_name': protocol_name,
            'sample_sizes': (len(rl_results), len(benchmark_results)),
            'statistical_tests': [],
            'multiple_comparison_result': None,
            'power_analysis': {},
            'overall_conclusion': '',
            'recommendations': []
        }
        
        # Perform primary statistical tests
        p_values = []
        
        for test_type in protocol.statistical_tests:
            if test_type == StatisticalTest.T_TEST_INDEPENDENT:
                result = self.validator.perform_t_test(rl_results, benchmark_results, paired=False)
            elif test_type == StatisticalTest.T_TEST_PAIRED:
                if len(rl_results) == len(benchmark_results):
                    result = self.validator.perform_t_test(rl_results, benchmark_results, paired=True)
                else:
                    continue  # Skip if sample sizes don't match
            elif test_type == StatisticalTest.MANN_WHITNEY_U:
                result = self.validator.perform_mann_whitney_u(rl_results, benchmark_results)
            else:
                continue  # Skip unsupported tests for now
            
            validation_results['statistical_tests'].append(result)
            p_values.append(result.p_value)
        
        # Multiple comparison correction
        if len(p_values) > 1:
            mc_result = self.validator.perform_multiple_comparisons(
                np.array(p_values), protocol.multiple_comparison_method, protocol.alpha_level
            )
            validation_results['multiple_comparison_result'] = mc_result
        
        # Power analysis
        if validation_results['statistical_tests']:
            primary_result = validation_results['statistical_tests'][0]
            if primary_result.effect_size:
                required_n = self.validator.calculate_required_sample_size(
                    abs(primary_result.effect_size), protocol.power_requirement, protocol.alpha_level
                )
                validation_results['power_analysis'] = {
                    'observed_power': primary_result.power,
                    'required_power': protocol.power_requirement,
                    'required_sample_size': required_n,
                    'actual_sample_size': min(primary_result.sample_sizes),
                    'adequately_powered': primary_result.power >= protocol.power_requirement if primary_result.power else False
                }
        
        # Generate overall conclusion
        validation_results['overall_conclusion'] = self._generate_conclusion(validation_results, protocol)
        validation_results['recommendations'] = self._generate_recommendations(validation_results, protocol)
        
        # Store results
        if experiment_id not in self.results:
            self.results[experiment_id] = []
        self.results[experiment_id].extend(validation_results['statistical_tests'])
        
        return validation_results
    
    def _generate_conclusion(self, results: Dict[str, Any], protocol: ValidationProtocol) -> str:
        """Generate overall conclusion from validation results"""
        if not results['statistical_tests']:
            return "No valid statistical tests performed"
        
        primary_test = results['statistical_tests'][0]
        
        # Check statistical significance
        significant = primary_test.p_value < protocol.alpha_level
        
        # Check effect size
        meaningful_effect = (primary_test.effect_size and 
                           abs(primary_test.effect_size) >= protocol.effect_size_threshold)
        
        # Check power
        adequate_power = (primary_test.power and 
                         primary_test.power >= protocol.power_requirement)
        
        if significant and meaningful_effect and adequate_power:
            return "Strong evidence supporting the hypothesis with adequate statistical power"
        elif significant and meaningful_effect:
            return "Evidence supporting the hypothesis, but statistical power may be insufficient"
        elif significant:
            return "Statistically significant result, but effect size may not be practically meaningful"
        else:
            return "No significant evidence supporting the hypothesis"
    
    def _generate_recommendations(self, results: Dict[str, Any], protocol: ValidationProtocol) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if not results['statistical_tests']:
            return ["Perform appropriate statistical tests"]
        
        primary_test = results['statistical_tests'][0]
        
        # Power recommendations
        if 'power_analysis' in results and results['power_analysis']:
            power_info = results['power_analysis']
            if not power_info['adequately_powered']:
                recommendations.append(
                    f"Increase sample size to {power_info['required_sample_size']} per group for adequate power"
                )
        
        # Effect size recommendations
        if primary_test.effect_size and abs(primary_test.effect_size) < protocol.effect_size_threshold:
            recommendations.append("Consider if observed effect size is practically meaningful")
        
        # Assumption violations
        if hasattr(primary_test, 'assumptions_met'):
            violated_assumptions = [k for k, v in primary_test.assumptions_met.items() if not v]
            if violated_assumptions:
                recommendations.append(f"Consider non-parametric alternatives due to violated assumptions: {violated_assumptions}")
        
        # Multiple comparisons
        if results['multiple_comparison_result']:
            mc_result = results['multiple_comparison_result']
            if not any(mc_result.rejected_hypotheses):
                recommendations.append("No hypotheses rejected after multiple comparison correction")
        
        return recommendations
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        report = {
            'protocols_defined': len(self.protocols),
            'experiments_validated': len(self.results),
            'protocol_summary': {},
            'overall_statistics': {},
            'recommendations': []
        }
        
        # Summarize protocols
        for name, protocol in self.protocols.items():
            report['protocol_summary'][name] = {
                'alpha_level': protocol.alpha_level,
                'power_requirement': protocol.power_requirement,
                'effect_size_threshold': protocol.effect_size_threshold,
                'sample_size_per_group': protocol.sample_size_per_group
            }
        
        # Overall statistics
        all_results = [result for results_list in self.results.values() for result in results_list]
        if all_results:
            p_values = [r.p_value for r in all_results if r.p_value is not None]
            effect_sizes = [r.effect_size for r in all_results if r.effect_size is not None]
            
            report['overall_statistics'] = {
                'total_tests': len(all_results),
                'significant_tests': len([p for p in p_values if p < 0.05]),
                'mean_effect_size': np.mean(effect_sizes) if effect_sizes else None,
                'median_p_value': np.median(p_values) if p_values else None
            }
        
        return report

# Example usage and testing
if __name__ == "__main__":
    # Create validation framework
    framework = ExperimentalValidationFramework()
    
    # Simulate some experimental data
    np.random.seed(42)
    rl_performance = np.random.normal(100, 15, 50)  # RL results
    cht_performance = np.random.normal(95, 12, 50)  # CHT results
    
    # Validate Tier 2 experiment
    validation_result = framework.validate_experiment(
        "tier2", rl_performance, cht_performance, "T2-1-CHT-OVERLOADED"
    )
    
    print("Statistical Validation Results:")
    print(f"Experiment: {validation_result['experiment_id']}")
    print(f"Protocol: {validation_result['protocol_name']}")
    print(f"Sample sizes: {validation_result['sample_sizes']}")
    
    for i, test_result in enumerate(validation_result['statistical_tests']):
        print(f"\nTest {i+1}: {test_result.test_name}")
        print(f"  p-value: {test_result.p_value:.6f}")
        print(f"  Effect size: {test_result.effect_size:.3f} ({test_result.effect_size_interpretation})")
        print(f"  Interpretation: {test_result.interpretation}")
    
    print(f"\nOverall conclusion: {validation_result['overall_conclusion']}")
    
    if validation_result['recommendations']:
        print("\nRecommendations:")
        for rec in validation_result['recommendations']:
            print(f"  - {rec}")
    
    # Generate validation report
    report = framework.generate_validation_report()
    print(f"\nValidation Framework Summary:")
    print(f"Protocols defined: {report['protocols_defined']}")
    print(f"Experiments validated: {report['experiments_validated']}")
    
    print("\nStatistical validation protocols ready for experimental execution!")

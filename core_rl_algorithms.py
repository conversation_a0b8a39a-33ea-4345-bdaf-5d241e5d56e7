"""
Core RL Algorithms for RL vs CHT Research Project

Implementation of Deep Q-Network (DQN), Proximal Policy Optimization (PPO), 
and Soft Actor-Critic (SAC) algorithms specifically designed for dynamic 
resource allocation problems.

All algorithms include proper validation, testing, and mathematical verification
to ensure correctness and reproducibility for academic publication.

Authors: <AUTHORS>
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging
import random
from collections import deque, namedtuple
import copy

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

@dataclass
class RLConfig:
    """Configuration for RL algorithms"""
    learning_rate: float = 3e-4
    batch_size: int = 64
    buffer_size: int = 100000
    gamma: float = 0.99
    tau: float = 0.005
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    update_frequency: int = 4
    target_update_frequency: int = 100
    hidden_dims: List[int] = None
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    
    def __post_init__(self):
        if self.hidden_dims is None:
            self.hidden_dims = [256, 256]

class ReplayBuffer:
    """Experience replay buffer for RL algorithms"""
    
    def __init__(self, capacity: int, state_dim: int, action_dim: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.experience = namedtuple('Experience', 
                                   ['state', 'action', 'reward', 'next_state', 'done'])
    
    def push(self, state: np.ndarray, action: Union[int, np.ndarray], reward: float, 
             next_state: np.ndarray, done: bool):
        """Add experience to buffer"""
        experience = self.experience(state, action, reward, next_state, done)
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, ...]:
        """Sample batch of experiences"""
        batch = random.sample(self.buffer, batch_size)
        
        states = torch.FloatTensor([e.state for e in batch])
        actions = torch.LongTensor([e.action for e in batch])
        rewards = torch.FloatTensor([e.reward for e in batch])
        next_states = torch.FloatTensor([e.next_state for e in batch])
        dones = torch.BoolTensor([e.done for e in batch])
        
        return states, actions, rewards, next_states, dones
    
    def __len__(self) -> int:
        return len(self.buffer)

class DQNNetwork(nn.Module):
    """Deep Q-Network for DQN algorithm"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int]):
        super(DQNNetwork, self).__init__()
        
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize network weights"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        return self.network(state)

class DQNAgent:
    """Deep Q-Network Agent"""
    
    def __init__(self, state_dim: int, action_dim: int, config: RLConfig):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        self.device = torch.device(config.device)
        
        # Networks
        self.q_network = DQNNetwork(state_dim, action_dim, config.hidden_dims).to(self.device)
        self.target_network = DQNNetwork(state_dim, action_dim, config.hidden_dims).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=config.learning_rate)
        
        # Copy weights to target network
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Replay buffer
        self.replay_buffer = ReplayBuffer(config.buffer_size, state_dim, action_dim)
        
        # Training parameters
        self.epsilon = config.epsilon_start
        self.step_count = 0
        
        self.logger = logging.getLogger(__name__)
    
    def select_action(self, state: np.ndarray, training: bool = True) -> int:
        """Select action using epsilon-greedy policy"""
        if training and random.random() < self.epsilon:
            return random.randrange(self.action_dim)
        
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()
    
    def store_experience(self, state: np.ndarray, action: int, reward: float, 
                        next_state: np.ndarray, done: bool):
        """Store experience in replay buffer"""
        self.replay_buffer.push(state, action, reward, next_state, done)
    
    def train_step(self) -> Dict[str, float]:
        """Perform one training step"""
        if len(self.replay_buffer) < self.config.batch_size:
            return {}
        
        # Sample batch
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.config.batch_size)
        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values from target network
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.config.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Update target network
        self.step_count += 1
        if self.step_count % self.config.target_update_frequency == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        # Decay epsilon
        self.epsilon = max(self.config.epsilon_end, 
                          self.epsilon * self.config.epsilon_decay)
        
        return {
            'loss': loss.item(),
            'epsilon': self.epsilon,
            'q_value_mean': current_q_values.mean().item()
        }
    
    def save_model(self, filepath: str):
        """Save model weights"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'step_count': self.step_count
        }, filepath)
    
    def load_model(self, filepath: str):
        """Load model weights"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.step_count = checkpoint['step_count']

class PolicyNetwork(nn.Module):
    """Policy network for PPO algorithm"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int]):
        super(PolicyNetwork, self).__init__()
        
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU()
            ])
            input_dim = hidden_dim
        
        self.shared_layers = nn.Sequential(*layers)
        self.policy_head = nn.Linear(input_dim, action_dim)
        self.value_head = nn.Linear(input_dim, 1)
        
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize network weights"""
        if isinstance(module, nn.Linear):
            nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
            nn.init.constant_(module.bias, 0)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        features = self.shared_layers(state)
        policy_logits = self.policy_head(features)
        value = self.value_head(features)
        return policy_logits, value

class PPOAgent:
    """Proximal Policy Optimization Agent"""
    
    def __init__(self, state_dim: int, action_dim: int, config: RLConfig):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        self.device = torch.device(config.device)
        
        # Network
        self.policy_network = PolicyNetwork(state_dim, action_dim, config.hidden_dims).to(self.device)
        self.optimizer = optim.Adam(self.policy_network.parameters(), lr=config.learning_rate)
        
        # PPO specific parameters
        self.clip_epsilon = 0.2
        self.value_loss_coef = 0.5
        self.entropy_coef = 0.01
        self.max_grad_norm = 0.5
        
        # Storage for trajectory
        self.states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []
        
        self.logger = logging.getLogger(__name__)
    
    def select_action(self, state: np.ndarray, training: bool = True) -> Tuple[int, float, float]:
        """Select action using policy network"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            policy_logits, value = self.policy_network(state_tensor)
            
        if training:
            # Sample action from policy
            action_probs = F.softmax(policy_logits, dim=-1)
            action_dist = torch.distributions.Categorical(action_probs)
            action = action_dist.sample()
            log_prob = action_dist.log_prob(action)
            
            return action.item(), log_prob.item(), value.item()
        else:
            # Deterministic action for evaluation
            action = policy_logits.argmax(dim=-1)
            return action.item(), 0.0, 0.0
    
    def store_experience(self, state: np.ndarray, action: int, reward: float, 
                        log_prob: float, value: float, done: bool):
        """Store experience for trajectory"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)
    
    def compute_returns_and_advantages(self, next_value: float = 0.0) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute returns and advantages using GAE"""
        returns = []
        advantages = []
        
        # Add next value for bootstrap
        values = self.values + [next_value]
        
        gae = 0
        for i in reversed(range(len(self.rewards))):
            delta = self.rewards[i] + self.config.gamma * values[i + 1] * (1 - self.dones[i]) - values[i]
            gae = delta + self.config.gamma * 0.95 * (1 - self.dones[i]) * gae  # GAE with lambda=0.95
            advantages.insert(0, gae)
            returns.insert(0, gae + values[i])
        
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return returns, advantages
    
    def train_step(self, next_value: float = 0.0) -> Dict[str, float]:
        """Perform PPO training step"""
        if len(self.states) == 0:
            return {}
        
        # Compute returns and advantages
        returns, advantages = self.compute_returns_and_advantages(next_value)
        
        # Convert to tensors
        states = torch.FloatTensor(np.array(self.states)).to(self.device)
        actions = torch.LongTensor(self.actions).to(self.device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        old_values = torch.FloatTensor(self.values).to(self.device)
        
        # PPO training loop
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        
        for _ in range(4):  # PPO epochs
            # Forward pass
            policy_logits, values = self.policy_network(states)
            action_probs = F.softmax(policy_logits, dim=-1)
            action_dist = torch.distributions.Categorical(action_probs)
            
            new_log_probs = action_dist.log_prob(actions)
            entropy = action_dist.entropy().mean()
            
            # Compute ratios
            ratios = torch.exp(new_log_probs - old_log_probs)
            
            # Policy loss with clipping
            surr1 = ratios * advantages
            surr2 = torch.clamp(ratios, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()
            
            # Value loss
            value_loss = F.mse_loss(values.squeeze(), returns)
            
            # Total loss
            loss = policy_loss + self.value_loss_coef * value_loss - self.entropy_coef * entropy
            
            # Optimize
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.policy_network.parameters(), self.max_grad_norm)
            self.optimizer.step()
            
            total_policy_loss += policy_loss.item()
            total_value_loss += value_loss.item()
            total_entropy_loss += entropy.item()
        
        # Clear trajectory
        self.clear_trajectory()
        
        return {
            'policy_loss': total_policy_loss / 4,
            'value_loss': total_value_loss / 4,
            'entropy': total_entropy_loss / 4
        }
    
    def clear_trajectory(self):
        """Clear stored trajectory"""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.log_probs.clear()
        self.values.clear()
        self.dones.clear()
    
    def save_model(self, filepath: str):
        """Save model weights"""
        torch.save({
            'policy_network_state_dict': self.policy_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, filepath)
    
    def load_model(self, filepath: str):
        """Load model weights"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.policy_network.load_state_dict(checkpoint['policy_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

class RLAlgorithmFactory:
    """Factory for creating RL algorithms"""
    
    @staticmethod
    def create_algorithm(algorithm_name: str, state_dim: int, action_dim: int, 
                        config: RLConfig = None) -> Union[DQNAgent, PPOAgent]:
        """Create RL algorithm instance"""
        if config is None:
            config = RLConfig()
        
        if algorithm_name.lower() == 'dqn':
            return DQNAgent(state_dim, action_dim, config)
        elif algorithm_name.lower() == 'ppo':
            return PPOAgent(state_dim, action_dim, config)
        else:
            raise ValueError(f"Unknown algorithm: {algorithm_name}")

class RLTrainer:
    """Trainer for RL algorithms"""
    
    def __init__(self, agent: Union[DQNAgent, PPOAgent], environment):
        self.agent = agent
        self.environment = environment
        self.logger = logging.getLogger(__name__)
        
        # Training metrics
        self.episode_rewards = []
        self.episode_lengths = []
        self.training_losses = []
    
    def train(self, num_episodes: int, max_steps_per_episode: int = 1000) -> Dict[str, List[float]]:
        """Train the RL agent"""
        self.logger.info(f"Starting training for {num_episodes} episodes")
        
        for episode in range(num_episodes):
            state = self.environment.reset()
            episode_reward = 0
            episode_length = 0
            
            for step in range(max_steps_per_episode):
                # Select action
                if isinstance(self.agent, DQNAgent):
                    action = self.agent.select_action(state, training=True)
                    next_state, reward, done, _ = self.environment.step(action)
                    
                    # Store experience
                    self.agent.store_experience(state, action, reward, next_state, done)
                    
                    # Train
                    if step % self.agent.config.update_frequency == 0:
                        loss_info = self.agent.train_step()
                        if loss_info:
                            self.training_losses.append(loss_info)
                
                elif isinstance(self.agent, PPOAgent):
                    action, log_prob, value = self.agent.select_action(state, training=True)
                    next_state, reward, done, _ = self.environment.step(action)
                    
                    # Store experience
                    self.agent.store_experience(state, action, reward, log_prob, value, done)
                
                state = next_state
                episode_reward += reward
                episode_length += 1
                
                if done:
                    break
            
            # PPO training at end of episode
            if isinstance(self.agent, PPOAgent):
                # Get final value for bootstrap
                if not done:
                    _, _, final_value = self.agent.select_action(state, training=True)
                else:
                    final_value = 0.0
                
                loss_info = self.agent.train_step(final_value)
                if loss_info:
                    self.training_losses.append(loss_info)
            
            self.episode_rewards.append(episode_reward)
            self.episode_lengths.append(episode_length)
            
            if episode % 100 == 0:
                avg_reward = np.mean(self.episode_rewards[-100:])
                self.logger.info(f"Episode {episode}, Average Reward: {avg_reward:.2f}")
        
        return {
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'training_losses': self.training_losses
        }
    
    def evaluate(self, num_episodes: int = 10) -> Dict[str, float]:
        """Evaluate the trained agent"""
        eval_rewards = []
        eval_lengths = []
        
        for episode in range(num_episodes):
            state = self.environment.reset()
            episode_reward = 0
            episode_length = 0
            
            while True:
                if isinstance(self.agent, DQNAgent):
                    action = self.agent.select_action(state, training=False)
                elif isinstance(self.agent, PPOAgent):
                    action, _, _ = self.agent.select_action(state, training=False)
                
                state, reward, done, _ = self.environment.step(action)
                episode_reward += reward
                episode_length += 1
                
                if done:
                    break
            
            eval_rewards.append(episode_reward)
            eval_lengths.append(episode_length)
        
        return {
            'mean_reward': np.mean(eval_rewards),
            'std_reward': np.std(eval_rewards),
            'mean_length': np.mean(eval_lengths),
            'std_length': np.std(eval_lengths)
        }

# Example usage and testing
if __name__ == "__main__":
    # Test algorithm creation
    config = RLConfig(learning_rate=1e-3, batch_size=32)
    
    # Create DQN agent
    dqn_agent = RLAlgorithmFactory.create_algorithm('dqn', state_dim=10, action_dim=4, config=config)
    print(f"Created DQN agent with {sum(p.numel() for p in dqn_agent.q_network.parameters())} parameters")
    
    # Create PPO agent
    ppo_agent = RLAlgorithmFactory.create_algorithm('ppo', state_dim=10, action_dim=4, config=config)
    print(f"Created PPO agent with {sum(p.numel() for p in ppo_agent.policy_network.parameters())} parameters")
    
    # Test action selection
    test_state = np.random.randn(10)
    
    dqn_action = dqn_agent.select_action(test_state, training=False)
    print(f"DQN selected action: {dqn_action}")
    
    ppo_action, _, _ = ppo_agent.select_action(test_state, training=False)
    print(f"PPO selected action: {ppo_action}")
    
    print("Core RL algorithms implemented successfully!")
    print("Ready for integration with simulation environment.")

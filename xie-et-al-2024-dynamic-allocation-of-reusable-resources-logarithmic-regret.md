

OPERATIONS RESEARCH
Vol. 73, No. 4, July–August 2025, pp. 2097–2124
https://pubsonline.informs.org/journal/opre    ISSN 0030-364X (print), ISSN 1526-5463 (online)

# Methods

# Dynamic Allocation of Reusable Resources: Logarithmic Regret in Overloaded Networks

**<PERSON><PERSON><PERSON>,<sup>a</sup> <PERSON><PERSON>,<sup>a,*</sup> <PERSON>m<PERSON><sup>b</sup>**

<sup>a</sup>Kellogg School of Management, Northwestern University, Evanston, Illinois 60208; <sup>b</sup>Industrial Engineering and Management Sciences, Northwestern University, Evanston, Illinois 60208

*Corresponding author

**Contact:** <EMAIL> (XX); <EMAIL>, https://orcid.org/0000-0001-9746-7755 (IG); <EMAIL>, https://orcid.org/0000-0001-6548-9378 (SK)

**Received:** August 19, 2022  
**Revised:** June 17, 2023; November 3, 2023; March 18, 2024  
**Accepted:** April 17, 2024  
**Published Online in Articles in Advance:** July 5, 2024

**Area of Review:** Stochastic Models

https://doi.org/10.1287/opre.2022.0429

**Copyright:** © 2024 INFORMS

**Abstract.** We study the problem of dynamically allocating reusable resources to customers of n types. There are d pools of resources and a finite number of units from each resource. If a customer request is accepted, the decision maker collects a type-dependent reward, and the customer occupies, for a random service time, one unit from each resource in a set of these. Upon service completion, these resource units become available for future allocation. This is a loss network: requests that are not accepted leave immediately. The decision maker's objective is to maximize the long-run average reward subject to the resource capacity constraint. A natural linear programming (LP) relaxation of the problem serves as an upper bound on the performance of any policy. We identify a condition that generalizes the notion of overload in single-resource networks (i.e., when d = 1). The LP guides our construction of a threshold policy. In this policy, the number of thresholds equals the number of resource types (hence, does not depend on the number of customer types). These thresholds are applied to a "corrected" headcount process. In the case of a single resource, the corrected head count is the number of resource units that are occupied. We prove that, in overloaded networks, the additive loss (or regret) of this policy benchmarked against the LP upper bound is logarithmic in the total arrival volume in the high-customer-volume, many-resource-units, asymptotic regime. No policy can achieve sublogarithmic regret. Simulations showcase the performance of the proposed policy.

**Funding:** X. Xie and I. Gurvich were supported by an Amazon Research Award and DRAGONS – Dynamic Resource Allocation Gains for Operational Networked Sharing, Department of Defense (Army) [Grant STTR A18B-T007]. S. Küçükyavuz was supported by an Office of Naval Research [Grant N00014-22-1-2602].

**Supplemental Material:** Data and code files are available at https://doi.org/10.1287/opre.2022.0429.

**Keywords:** sequential resource allocation • regret • linear programming relaxation • loss networks • Lyapunov function • reusable resources

## 1. Introduction

In dynamic resource allocation problems, a decision maker (DM) allocates a finite number of resource units to sequentially arriving customers in order to maximize the revenue collected from accepted customer requests. Customers leave immediately if their request is not accepted.

Dynamic stochastic knapsack is a family of resource allocation problems whereby, once a unit of resource is consumed, it cannot be allocated again in the future. These have applications in revenue management when, for example, the resources are seats on a specific flight.

In this paper, we focus on the case in which resources are reusable. They are used by an accepted request/customer for a (random) duration and are returned to the DM at the conclusion of service (or processing). Rental services (hotels, cars, or cloud computing units) are often modeled as networks of reusable resources. In its fullest (network) generality, our model is standard in the study of telecommunication networks; see Hui (2012). In these networks, a communication (or a call) requires, for its duration, the simultaneous occupation of multiple connected links leading from a source node to a destination node; the links are the resources, and the collection required by a call is its route. If one of the links in the route is fully occupied, the call is lost.

The original motivation for this study was the optimization of resource allocation across multiple military tasks (Gurvich and Intelligent Automation 2018–2021). Such simultaneous occupation of multiple resources is also relevant in consulting or information technology (IT) services. Here, the types of resources correspond to the different professionals required to deliver the

2097

---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2098                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

product. A service engagement might require, for example, three database architects, five Java programmers, a network specialist, and so on. These resources must be allocated to the client for the duration of the engagement; see Hu et al. (2010) and Cao et al. (2011) for a detailed description of models used for an IBM line of IT services.

The fundamental trade-off is the same in these applications: accepting a request—and occupying resources—may prevent the acceptance of a later request that requires an overlapping set of resources; one might want to reserve some capacity for highly valuable requests.

In the simplest instance of these problems (see Figure 1, left) there is a single resource with q units (say q hotel rooms) serving multiple types of customers. Type i customers arrive according to a Poisson(λ<sub>i</sub>) process and request a single unit of the resource. If a type i request is accepted, the DM collects a reward, r<sub>i</sub> > 0, and a unit of the resource is occupied for an Exp(μ<sub>i</sub>) service time after which the resource unit becomes available for future allocations. The objective of the DM is to maximize the expected long-run average reward subject to the constraint that no more than the q units of the resource can be occupied at any given time.

With homogeneous service times (μ<sub>i</sub> ≡ μ), the optimal policy is a trunk reservation policy (Miller 1969, Lippman 1975), wherein an arriving type i customer is accepted if and only if there are more than R<sub>i</sub> ∈ N ∪ {∞} units of resource available; R<sub>i</sub> = 1 means that type i customers are accepted whenever there is a resource unit available, and R<sub>i</sub> = ∞ means that these customers' requests are always rejected regardless of the number of available resource units. Some customer types might have finite thresholds that are strictly greater than one, and these thresholds satisfy a natural monotonicity: the higher the reward that type i brings, the lower the acceptance threshold.

With heterogeneous service times (μ<sub>i</sub> ≠ μ<sub>j</sub>), the optimal decision is complex and generally depends on the number of customers of each type present in the system rather than the head count, which tracks only the total number of occupied resource units (Örmeci et al. 2001).

A high-volume, many-server, asymptotic framework (λ<sub>i</sub><sup>N</sup> = Nλ<sub>i</sub>, q<sup>N</sup> = Nq) exposes characteristics of "good" policies. The simplicity of good policies depends on the regime: underloaded, critically loaded, or overloaded; see Section 1.1. A single-resource corollary of our general result is that, in the overloaded regime, the appealingly simple trunk reservation policy with a single threshold has a gap from a deterministic upper bound that is logarithmic in N. This means that no policy, including the possibly complex optimal policy, can improve on this simple prescription by more than log N. In fact, as we prove, such logarithmic regret is the best one can hope for: no policy can get closer than log N to the deterministic upper bound; in the overloaded regime, log N captures precisely the cost of stochasticity.

In the single-resource case, regardless of the number of types n, our policy uses a single threshold applied to the class with the lowest value r<sub>i</sub>μ<sub>i</sub>. We are interested in networks in which different request/customer types consume different (subsets of) resources; see Figure 1, right.

The most intuitive generalization of trunk reservation to d resources suggests assigning # types × # resources thresholds R<sub>ij</sub> so that a type i customer is accepted only if there are more than R<sub>ij</sub> available units of resource j. If the network is overloaded, we show, a policy with only d (≪ # resources) thresholds achieves a logarithmic regret. In Figure 1, right, requests of type 3 require a unit of resource b and a unit of resource c. Under our policy, a threshold is applied to either b or c but not both: we accept a type 3 request whenever there are units available of resource b (without any requirement on the number of such units) but demand that there are at least an order of log N nominal units available of resource c. "Nominal" here refers to the fact that, instead of applying the threshold to the number of busy (or available) units of a resource—the resource's head count—we apply it to a corrected head count.

Our proposed policy produces a regret that is logarithmic in N; the formal statement appears in Theorem 4.1 after we introduce the key building blocks of our policy. The key ingredients are as follows:

1. We identify a network version of overload and establish properties of the linear programming (LP) relaxation of the problem under the network overload

**Figure 1. Two Examples of Networks**

<table>
<tr>
<td>

**Left Network (Single Resource):**
- Customer Types: 1, 2, 3, 4
- All connect to single Resource Unit: a

</td>
<td>

**Right Network (Multiple Resources):**
- Customer Types: 1, 2, 3, 4, 5, 6, 7
- Resource Units: a, b, c, d
- Connections:
  - Type 1 → a, b
  - Type 2 → a, b, c
  - Type 3 → b, c
  - Type 4 → a, c, d
  - Type 5 → c, d
  - Type 6 → c, d
  - Type 7 → d

</td>
</tr>
</table>

Notes. (Left) A network with a single resource. (Right) A network with multiple resources for which each type of customer requires the simultaneous possession of multiple resources.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                       2099

condition. Overload guarantees the existence of a perfect matching between less-preferred customer types—as identified by the LP solution—and resources; this matching produces pairs of resources and customer types—for each resource j, there is a single request type i<sub>j</sub> coupled with it—and guides placement of thresholds. We show that, when the network overload condition is violated, the gap of the optimal online policy from the deterministic LP upper bound generally scales as √N.

2. We introduce a policy based on corrected head count processes, which are proxies for the true head counts. The thresholds are applied to these proxies. Informally speaking, the threshold policy based on the corrected head count guides the allocation to its desired levels as dictated by the LP benchmark.

3. We prove that, in overloaded networks, the regret of our policy—its additive reward loss (or approximation gap) relative to the LP solution—is at most logarithmic in the scaling factor N.

4. We show that this is the best possible: no other policy can get closer than log N to the LP upper bound. We also prove that state dependency of the policy is necessary: static policies (e.g., randomized acceptance) induce a larger approximation gap.

available resource units upon arrival is below the trunk reservation level for that customer. Key (1990) and Reiman (1991) show that the optimal trunk reservation level is logarithmic in the number of servers (resource units).

In the heterogeneous variant of the single-resource model in which either different customers request different numbers of resource units or customers have different service rates, the optimal policy is generally not of a trunk reservation type (Ross and Tsang 1989, Örmeci et al. 2001, Örmeci and van der Wal 2006).

Trunk reservation policies are practically appealing and, though not optimal, deliver asymptotically optimal performance in certain conditions. A well-studied limiting regime is one in which service rates are fixed, while both the arrival rates and the quantities of resource units scale up linearly at the same rate. Puhalskii and Reiman (1998) consider a critically loaded regime with a single resource type and multiple types of customers with different (i.e., heterogeneous) service times. They prove that the trunk reservation policy is asymptotically optimal (in a central limit theorem sense) under the requirement that the most valuable requests are also the ones with the longest service time.

Hunt and Kurtz (1994) and Bean et al. (1997) prove that, under a large family of policies (that includes trunk reservation), the number of customers in service—scaled in a strong law scaling—converges to the solution of an integral equation. Subsequent work by Bean et al. (1995) and Hunt and Laws (1997) proves that—with a single resource—a trunk reservation policy is asymptotically optimal in fluid scale (i.e., the optimality gap is o(N), where N is the scaling factor).

Iyengar and Sigman (2004) consider a problem that is more general than the one we study here. The DM not only decides whether to accept a customer, but also determines the resource allocation (out of a type-dependent set of such). They devise a control policy that is asymptotically optimal in the approximation ratio sense in the single resource case (d = 1). For d > 1, they establish a lower bound on the approximation ratio. For our more restricted model, we obtain a logarithmic additive gap, a stronger notion that implies, in particular, asymptotic optimality in the approximation ratio sense.

Pricing, when adjustable, adds a control lever. Paschalidis and Tsitsiklis (2000) study the pricing problem in the case of a single resource and derive structural properties of the optimal policy. They prove that static pricing is asymptotically optimal, in the fluid scaling sense, in the high-volume, many-server regime. Paschalidis and Liu (2002) extend this result to the network case of multiple types of resource units (servers) with fixed routing and show that a static pricing policy remains asymptotically optimal. In our case, prices (and rewards) are fixed.

## 1.1. Related Literature

Our work has natural connections to two overlapping streams of literature: queueing theory (loss networks) and revenue management (networks of reusable resources).

### 1.1.1. Loss Networks

In the queueing literature, loss networks are a class of networks with no buffers. If not accepted, a customer leaves the system immediately instead of being put on hold in a queue. Customers of different types arrive sequentially and request a set of resource units (servers) for a random duration (service time).

The study of loss networks was originally motivated by telecommunication networks, and the early focus has been on analyzing the blocking probabilities under specific practical control policies. Complete sharing, for example, is a policy that accepts all arriving requests whenever feasible. It induces a stationary distribution that has a so-called product form structure, which renders the blocking probability calculations tractable; see Kelly (1986) and the comprehensive review Kelly (1991). See also Jung et al. (2019) and the references therein for recent progress in this area.

The complete sharing policy is not optimal for reward maximization when customers are heterogeneous in the rewards they bring and/or the resources they consume. In the case of a single resource and homogeneous service times, Miller (1969) and Lippman (1975) prove that the optimal policy is a trunk reservation policy that reserves resource units for the more valuable customers and rejects a less valuable customer if the number of
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2100                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

## 1.1.2. Revenue Management with Reusable Resources.

Our work is closely related to the canonical quantity-based (admission control) network revenue management problems; see, for example, Williamson (1992), Gallego and van Ryzin (1997), Reiman and Wang (2008), Jasin and Kumar (2012), and Bumpensanti and Wang (2020) as well as the book by Talluri and van Ryzin (2004). Motivated by airline revenue management problems, much of this work considers resource units that are either perishable or are allocated at the end of the decision horizon.

The revenue management literature on reusable resources is more recent and, hence, relatively scarce. Levi and Radovanović (2010) consider the allocation of a single pool of reusable resources to multiple types of customers and devise a class selection policy based on the linear programming relaxation of the problem with guarantees on its approximation ratio. Chen et al. (2017) consider a variant in which customers request the resource units in advance with deterministic starting times and durations. A modified class selection policy, they show, achieves asymptotic optimality in the approximation ratio sense.

Regulation of arrivals through pricing is considered by Xu and Li (2013), who study pricing in a cloud computing platform and characterize structural properties of the optimal policy. Lei and Jasin (2020) study the pricing problem when customers have deterministic advance reservation times and service times. They develop a policy that is based on a deterministic relaxation of the problem and prove an upper bound on its regret. Besbes et al. (2021) consider a pricing problem for a combined objective of revenue, market share, and service level. They prove that a static pricing policy can simultaneously achieve 80% of all three metrics relative to the optimal dynamic programming policy. Jia et al. (2024) tackle the problem of pricing when some of the parameters must be learned.

Recent literature also considers the case in which customers make choices and the DM can optimize (dynamically) the assortment an arriving customer sees. Each arriving customer is shown a set of different products and chooses one of them. Owen and Simchi-Levi (2017) devise pricing and assortment optimization policies with provable approximation ratio lower bounds. Rusmevichientong et al. (2020) take a different approach to produce a policy with a provable approximation ratio. Baek and Ma (2022) generalize this guarantee to more general settings in which each product in an assortment consists of multiple types of resource units. Policies for dynamic assortment optimization with reusable resources are developed in Feng et al. (2024), Goyal et al. (2020), and Gong et al. (2021) together with approximation ratio guarantees.

In our setting, the assortment per customer type is fixed, but customers do not necessarily consume a single resource unit (a single selection from the assortment); they might consume multiple resource units of different types. Our notion of optimality is stronger than (and implies) approximation ratio optimality. The policy we propose achieves the optimal approximation gap scaling relative to the linear programming benchmark. The price we pay for this optimality is (i) the knowledge of the arrival rates (at least up to a small perturbation) and (ii) a network overload condition that is a natural generalization of the single-resource notion. The presence of overload depends on both the graph and the other primitives (arrival rates, service rates, and rewards).

## 1.2. Organization of the Paper

The rest of the paper is organized as follows. Our model is described in Section 2, in which we also introduce the deterministic LP relaxation of the problem. In Section 3, we introduce the network overload condition and study its implications. We construct our corrected head count threshold (CHT) policy and state the main optimality result in Section 4. The main proofs appear in Sections 5 and 6; all lemmas are proved in the appendix. Simulation experiments are reported in Section 7.

### Notation.

Given a Markov chain X = (X<sub>t</sub>, t ≥ 0), we let P<sub>x</sub>{B} be the likelihood of the event B when X<sup>0</sup> = x. Similarly, P<sub>Π</sub>{B} is the likelihood of that event when X<sup>0</sup> ~ Π; E<sub>x</sub>[·] and E<sub>Π</sub>[·] denote then the corresponding expectations. Throughout the remainder of the paper, for two nonnegative sequences a<sup>N</sup>, b<sup>N</sup> and a nonnegative function h : R<sub>+</sub> → R<sub>+</sub>, a<sup>N</sup> = b<sup>N</sup> + O(h(N)) means that |a<sup>N</sup> − b<sup>N</sup>| = O(h(N)); the same is true for the scaling notation o(·) and Ω(·).

We introduce various process variables throughout the paper. For easy reference we provide a notation index in Table 2 at the end of the paper.

## 2. The Model: Dynamic Allocation of Reusable Resources

There are n types of customers labeled by i ∈ [n] ≡ {1, . . . , n} and d resources labeled by j ∈ [d]. There are q<sub>j</sub> reusable units of resource j.

Customers of type i ∈ [n] arrive following a Poisson process with rate λ<sub>i</sub> > 0 and request the simultaneous possession of a unit from each of multiple resources. The resource consumption is encoded in an adjacency matrix A ∈ {0, 1}<sup>d×n</sup> such that A<sub>ji</sub> = 1 if type i customers require a unit of resource j. We visualize the network topology as in Figure 1, right. An edge between request type i (a circle) and a resource j (a rectangle) corresponds to A<sub>ji</sub> = 1.

If a type i customer's request is accepted, the decision maker collects a reward r<sub>i</sub>, and the requested resource
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                                       2101

units are allocated and occupied for an exponentially distributed amount of time with mean μ<sub>i</sub><sup>-1</sup>. For example, an accepted request of type 1 in Figure 1, right, occupies simultaneously one unit from each of the resources b, c, and d. These three units are released simultaneously after an exponential amount of time with mean μ<sub>1</sub><sup>-1</sup>.

For each i ∈ [n], we denote by S(i) := {j ∈ [d] : A<sub>ji</sub> = 1} the set of resources required by type i customers, and for each j ∈ [d], we denote by A(j) := {i ∈ [n] : A<sub>ji</sub> = 1} the set of customer types requiring resource j. To avoid trivialities, we assume that S(i), A(j) ≠ ∅ for all i ∈ [n], j ∈ [d]. A customer's request can be accepted—accepting it is feasible—only if all the required resources have available units when the request is made. The decision maker can reject a request even if it is feasible to accept it. Once a customer is accepted, the requested resource units are immediately occupied; these resource units become simultaneously available at the conclusion of the customer's service. Preemption is not allowed: once accepted, a request is processed to completion. Arrival processes are assumed independent across customer types, and resource occupation times are independent across customers.

The objective of the decision maker is to maximize the long-run average collected reward subject to the resource constraint. Let r = (r<sub>1</sub>, ..., r<sub>n</sub>) be the reward vector and D<sup>π</sup> = (D<sub>1</sub><sup>π</sup>(t), ..., D<sub>n</sub><sup>π</sup>(t)) be the vector of acceptances; that is, D<sub>i</sub><sup>π</sup>(t) is the number of type i customers accepted by time t under policy π, both column vectors. The performance of the optimal policy is given by

$$R^* = \sup_π \lim \inf_{t↑∞} \frac{1}{t} E[r'D^π(t)],$$

where the sup is taken over all nonanticipating nonpreemptive policies.

The optimal policy can be obtained via dynamic programming. The state descriptor X<sup>t</sup> is n-dimensional, and X<sub>i</sub><sup>t</sup> tracks the number of type i customers in service at time t. The state space has ∏<sub>i=1</sub><sup>n</sup> min<sub>j∈S(i)</sub>q<sub>j</sub> states, which, except the simplest networks, is computationally prohibitive.

Instead, our goals are to (i) characterize (indirectly) the performance of the optimal policy, specifically its regret (or approximation gap): how close its performance is to a natural deterministic upper bound, and (ii) offer a simple policy that achieves the optimal regret scaling in a high-volume, many-server regime.

The linear programming relaxation of the problem is the starting point of our policy design. Given a policy π, let z<sub>i</sub><sup>π</sup> = lim inf<sub>t↑∞</sub> <sup>1</sup>/<sub>t</sub> E[D<sub>i</sub><sup>π</sup>(t)]. Because no more type i requests can be accepted than those arriving, we must have z<sub>i</sub><sup>π</sup> ≤ λ<sub>i</sub>. By Little's law, z<sub>i</sub><sup>π</sup>/μ<sub>i</sub> is the long-run average number of type i customers in service (occupying resources), so it must be the case that ∑<sub>i</sub> A<sub>ji</sub>z<sub>i</sub><sup>π</sup>/μ<sub>i</sub> ≤ q<sub>j</sub>, for all j ∈ [d].

Because each policy π must satisfy these constraints, the following linear program is an upper bound on the long-run average reward of any admissible policy:

$$\max_{z∈R_+^n} r'z$$

$$\text{s.t.} \quad A(z/μ) ≤ q,$$

$$z ≤ λ.$$

Here, z/μ = (z<sub>1</sub>/μ<sub>1</sub>, ..., z<sub>n</sub>/μ<sub>n</sub>). Changing variables y ← z/μ, we rewrite this as

$$R(q, λ/μ) := \max_{y∈R_+^n} r'<sub>μ</sub>y$$

$$\text{s.t.} \quad Ay ≤ q, \quad \text{(LP)}$$

$$y ≤ λ/μ,$$

where r<sub>μ</sub> = (r<sub>1</sub>μ<sub>1</sub>, ..., r<sub>n</sub>μ<sub>n</sub>) and λ/μ is the vector with elements λ<sub>i</sub>/μ<sub>i</sub>, i ∈ [n].

The value R(q, λ/μ) is an upper bound on the expected long-run average reward, R<sup>π</sup>, collected by a nonanticipating nonpreemptive policy π:

$$R^π ≤ R^* ≤ R(q, λ/μ).$$

The optimal solution y<sup>*</sup> of (LP) yields a partition of the customer types:
• It accepts all type i customers with y<sub>i</sub><sup>*</sup> = λ<sub>i</sub>/μ<sub>i</sub> (the preferred types).
• It accepts a fraction of type i customers with y<sub>i</sub><sup>*</sup> ∈ (0, λ<sub>i</sub>/μ<sub>i</sub>) (less preferred types).
• It rejects all type i customers with y<sub>i</sub><sup>*</sup> = 0 (rejected types).

The groups

$$A_p := \{i ∈ [n] : y_i^* = λ_i/μ_i\}, \quad \text{(preferred)}$$

$$A_{lp} := \{i ∈ [n] : y_i^* ∈ (0, λ_i/μ_i)\}, \quad \text{(less preferred)}$$

$$A_0 := \{i ∈ [n] : y_i^* = 0\}, \quad \text{(rejected)}$$

form a partition of [n]: A<sub>p</sub> ∪ A<sub>lp</sub> ∪ A<sub>0</sub> = [n].

**Example 2.1** (The Single-Resource Case). For d = 1, (LP) has a single capacity constraint and a packing solution. Suppose that types are labeled in decreasing order of r<sub>i</sub>μ<sub>i</sub>: r<sub>1</sub>μ<sub>1</sub> > r<sub>2</sub>μ<sub>2</sub> > ⋯ > r<sub>n</sub>μ<sub>n</sub>, and let i<sup>*</sup> = max{i : ∑<sub>k=1</sub><sup>i</sup> λ<sub>k</sub>/μ<sub>k</sub> ≤ q}. The optimal solution has y<sub>l</sub><sup>*</sup> = λ<sub>l</sub>/μ<sub>l</sub> for all l ≤ i<sup>*</sup>, y<sub>i*+1</sub><sup>*</sup> = q - ∑<sub>l=1</sub><sup>i*</sup> λ<sub>l</sub>/μ<sub>l</sub> and y<sub>l</sub> = 0 otherwise. The optimal value is R(q, λ/μ) = ∑<sub>i=1</sub><sup>i*</sup> r<sub>i</sub>λ<sub>i</sub> + r<sub>i*+1</sub>μ<sub>i*+1</sub>(q - ∑<sub>l=1</sub><sup>i*</sup> λ<sub>l</sub>/μ<sub>l</sub>).

We assume for the rest of the paper and without loss of generality that A<sub>0</sub> = ∅. Our policy does not serve those requests and achieves logarithmic regret relative to the LP-based upper bound.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2102                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

## 2.1. The High-Volume, Many-Server Regime

We study reward maximization in a standard (e.g., Hunt and Kurtz 1994, Hunt and Laws 1997, Puhalskii and Reiman 1998) high-volume, many-server regime. The customer arrival rates and the number of resource units scale at the same rate:

$$\lambda_i^N = N\lambda_i, i \in [n], \quad \text{and} \quad q_j^N = Nq_j, j \in [d],$$

where $\lambda_i, i \in [n]$ and $q_j, j \in [d]$ are strictly positive.

With this scaling, $\mathcal{R}(q^N, \lambda^N/\mu) = N\mathcal{R}(q, \lambda/\mu)$ so that

$$\mathcal{R}^{\pi,N} \leq \mathcal{R}(q^N, \lambda^N/\mu) = N\mathcal{R}(q, \lambda/\mu),$$

for any policy $\pi$ in the Nth network. We add the superscript N to denote quantities for the Nth network; $\mathcal{R}^{*,N}$, for example, is the reward collected by the optimal policy in the Nth network.

**Remark 2.1 (An Offline Upper Bound).** The number of type i customers in the system at a given time t, it is easily seen, is bounded from above by that number in an infinite-server queue with arrival rate $\lambda_i$ and service rate $\mu_i$; this number is distributed, in steady state, as a Poisson random variable with mean $\lambda_i^N/\mu_i$. Then, $\mathcal{R}^{*,N} \leq \mathbb{E}[\mathcal{R}(q^N, Y^N)] \leq \mathcal{R}(q^N, \lambda^N/\mu)$, where

$$\mathcal{R}(q^N, Y^N) := \begin{cases}
\max_{y \in \mathbb{R}^n} & r'_\mu y \\
\text{s.t.} & Ay \leq q^N, \\
& y \leq Y^N,
\end{cases} \quad (1)$$

with $Y_i^N$ a Poisson random variable with mean $\lambda_i^N/\mu_i$ and $Y^N = (Y_i^N, i \in [n])$. It is easy to show that, under Assumption 3.1, $\mathcal{R}(q^N, \lambda^N/\mu) - \mathbb{E}[\mathcal{R}(q^N, Y^N)] = O(1)$ so that the offline static upper bound is as crude as the deterministic upper bound. Nevertheless, this offline benchmark is useful in showing that, when our overload assumption is violated, the gap of the optimal policy from the deterministic LP upper bound can be substantial; see Lemma 3.1.

uniqueness and nondegeneracy of the primal, the dual has a unique solution. The unique solution pair (of the primal and the dual) must satisfy strict complementarity (see, e.g., theorem 10.7 of Vanderbei 1998). In turn, the dual variables of all binding resource constraints are strictly positive: increasing the capacity of any of these binding resource constraints leads to an increase in objective function value. This means that the resource constraints held at equality are binding in a strong sense.

The uniqueness of the dual variables corresponding to the demand constraints is, in fact, necessary for a logarithmic regret. For the following, recall that $\mathcal{R}^{*,N} \leq \mathcal{R}(q^N, \lambda^N/\mu)$.

**Lemma 3.1 (Necessity of Dual Uniqueness).** Suppose that the dual to (LP) has two optimal solutions that differ in the shadow prices of the demand constraints. Then,

$$\mathcal{R}(q^N, \lambda^N/\mu) - \mathcal{R}^{*,N} = \Omega(\sqrt{N}).$$

The intuition here is simple and best understood through the offline upper bound (1). This upper bound is a stochastic perturbation of the deterministic one (in which the perturbations are centered on the demand and service times, in turn, on the maximal mean occupancy $\lambda/\mu$). When the shadow prices of the demand constraints are not unique, perturbations of the demand in different directions have different effects on the dual (and, hence, primal) objective function value. The perturbation of the Poisson right-hand side $Y^N$ in (1) around its mean is symmetric and of the order of $\sqrt{N}$, and because of the dual nonuniqueness, the effects of these stochastic perturbations on the objective do not average out. Thus, $\mathcal{R}(q^N, \lambda^N/\mu) - \mathcal{R}^{*,N} \geq \mathcal{R}(q^N, \lambda^N/\mu) - \mathbb{E}[\mathcal{R}(q^N, Y^N)] = \Omega(\sqrt{N})$.

Assumption 3.1 allows for nonbinding resource constraints at optimality, that is, for the existence of underutilized resources that have strictly positive slack. These resources and their constraints can be removed from (LP) without affecting the optimal solution and its value. For the remainder of the paper, we assume that all d resource constraints are binding. We revisit this simplification upon the conclusion of the proofs; see Remark 6.1.

## 3. Overloaded Networks

In the single resource case, a resource is, intuitively speaking, overloaded if there is more demand than the server can handle: $q < \sum_{i \in [n]} \lambda_i/\mu_i$. We require, in addition, that $y_{i*+1} > 0$, which makes the LP nondegenerate; recall Example 2.1. The following condition is a network generalization.

**Assumption 3.1 (Network Overload).** The linear program (LP) has a unique and nondegenerate solution, and at least one resource constraint is, at optimality, tight.

Two implications of this assumption justify referring to networks that satisfy this assumption as overloaded. The obvious one is that there is a resource constraint held at equality. But this is not all. Because of the

**Lemma 3.2.** Suppose that Assumption 3.1 holds; then, $|A_{lp}| = d$. Moreover, the submatrix of A that has only the columns for $i \in A_{lp}$ is full rank.

The optimization problem (LP) is defined on a bipartite graph, for example, Figure 1, right, in which customer types are on one side of the partition and resources are on the other side. There is an edge between customer type $i \in [n]$ and resource $j \in [d]$ if $A_{ji} = 1$.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                          2103

**Figure 2.** (Color online) An Example with Three Customer Types and Two Resource Types

<table>
<tr>
<td>Customer Type 1</td>
<td>Customer Type 2 (colored)</td>
<td>Customer Type 3 (colored)</td>
</tr>
<tr>
<td>Connected to: Resource a</td>
<td>Connected to: Resource a, Resource b</td>
<td>Connected to: Resource b</td>
</tr>
</table>

Notes. Less preferred types are marked in color. Edges corresponding to a type and its paired resource are marked with ∥.

{1, 2, 3} in Figure 2. Type 2 customers request a unit of both type a and type b resources, type 1 customers request a unit of type a resource, and type 3 customers request a unit of type b resource.

We set the resource units to q = (7, 6) and the customer type parameters to λ = (3, 2, 5), μ<sup>-1</sup> = (2, 1, 3) (λ/μ = (6, 2, 15)). The reward vector is r = (5, 1, 2) so that r<sub>μ</sub> = (5/2, 1, 2/3). The (LP) has the unique nondegenerate solution y<sup>*</sup> = (6, 1, 5)—y<sub>1</sub><sup>*</sup> = λ<sub>1</sub>/μ<sub>1</sub>, y<sub>2</sub><sup>*</sup> ∈ (0, λ<sub>2</sub>/μ<sub>2</sub>), y<sub>3</sub><sup>*</sup> ∈ (0, λ<sub>3</sub>/μ<sub>3</sub>), and the dual variables for the resource constraints are α<sup>*</sup> = (1/3, 2/3) > 0.

In this case, types 2 and 3 are the less-preferred types (A<sub>lp</sub> = {2, 3}) and are marked in color in Figure 2. The residual graph contains types 2 and 3 and both resources. The unique perfect matching is {(2, a), (3, b)} so that i<sub>a</sub> = 2, i<sub>b</sub> = 3.

## Definition 3.1 (The lp-Residual Graph)

The lp-residual graph, G<sub>lp</sub>, is the graph obtained by removing all preferred types i ∈ A<sub>p</sub> (and the edges that connect them to resources).

It follows from Lemma 3.2 that the lp-residual graph is bipartite with d vertices in each of its constituent sets: d customer types and d resources. A perfect matching in a graph is a set of edges such that each vertex is incident to exactly one edge. The incidence matrix of the lp-residual graph is the submatrix of A that has only the columns for i ∈ A<sub>lp</sub>; by Lemma 3.2, it is full rank. This guarantees the existence of a perfect matching in the lp-residual graph (see, e.g., Tutte 1947, theorem 7.3 of Motwani and Raghavan 1995).

If there exist multiple perfect matchings in the residual graph (see, e.g., Figure 4) we pick one arbitrarily. For each resource j, we write i<sub>j</sub> for the (less-preferred) request type that is matched with resource j in this perfect matching. For each type i ∈ A<sub>lp</sub>, j<sub>i</sub> is the resource matched to i.

We use three network examples throughout this paper. Examples 3.1 and 3.2 have a unique perfect matching in the residual graph. The former is the simplest possible example with more than one resource and is useful to illustrate basic constructions and develop intuition. The latter is a more elaborate example and is a useful test for the numerical performance of our proposed algorithm. Example 3.3 has multiple perfect matchings. The corrected head count process that we introduce later and that plays a role in our algorithm takes on a less intuitive form in this case.

**Example 3.1.** Consider the network with two types of resource units {a, b} and three types of customers

**Example 3.2.** Consider the network with seven customer types and four resource types in Figure 3, left. We set the resource units to q = (11, 19, 14, 7) and the customer type parameters to λ = (2, 3, 5, 1, 6, 2, 3), μ<sup>-1</sup> = (1, 3, 2, 3, 5, 4, 2), so that λ/μ = (2, 9, 10, 3, 30, 8, 6). Finally, we take the reward r = (2, 1, 3, 5, 1, 6, 5) so that r<sub>μ</sub> = (2, 1/3, 3/2, 12/3, 1/5, 3/2, 5/2).

The (LP) has the unique nondegenerate solution y<sup>*</sup> = (1, 8, 5, 3, 5, 8, 6) with the resource constraint dual variables equal to α<sup>*</sup> = (2/15, 1/5, 13/10, 1/2). The unique perfect matching in the residual graph is {(1, d), (2, a), (3, c), (5, b)}.

**Example 3.3.** Consider the network with six customer types and three resource types in Figure 4, left. We set the resource units to q = (2, 2, 2) and the customer type parameters to λ = (2, 2, 2, 1/2, 1/3, 1/4), μ<sup>-1</sup> = (1, 1, 1, 2, 3, 4), so that λ/μ = (2, 2, 2, 1, 1, 1). Finally, we take the reward r = (1, 1, 1, 4, 6, 8) so that r<sub>μ</sub> = (1, 1, 1, 2, 2, 2). The (LP) has the unique nondegenerate solution y<sup>*</sup> = (1, 1, 1, 1, 1, 1), and the dual variables for the resource constraints are α<sup>*</sup> = (1/2, 1/2, 1/2). This network has two perfect matchings: {(1, a), (2, b), (3, c)} and {(1, b), (2, c), (3, a)}.

**The Target Allocation Levels.** Let A<sub>lp</sub> be the incidence matrix of the lp-residual graph. This is the square d × d submatrix of A that has all d rows and the d columns corresponding to the less-preferred types i ∈ A<sub>lp</sub>; A<sub>lp</sub>

**Figure 3.** (Color online) An Example with Seven Customer Types and Four Resource Types

<table>
<thead>
<tr>
<th>Left Graph - Original Network</th>
<th>Right Graph - lp-residual graph</th>
</tr>
</thead>
<tbody>
<tr>
<td>
Customer Types: 1, 2 (colored), 3 (colored), 4, 5 (colored), 6, 7<br>
Resource Types: a, b, c, d<br>
Connections shown with edges between customer types and resources
</td>
<td>
Customer Types: 1, 2, 3, 5<br>
Resource Types: a, b, c, d<br>
Reduced network showing only less-preferred types
</td>
</tr>
</tbody>
</table>

Notes. (Left) Less-preferred types are marked in color. Edges corresponding to a type and its paired resource are marked with ∥. (Right) The lp-residual graph.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2104                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

**Figure 4.** (Color online) Example with Either Multiple Perfect Matchings or None

[The figure shows two network diagrams side by side. The left diagram has 3 numbered nodes (1, 2, 3) at the top connected to 3 lettered nodes (a, b, c) in the middle, which connect to 3 numbered nodes (4, 5, 6) at the bottom. The right diagram is similar but has an additional node 7 at the bottom and node d in the middle layer, creating a 4×4 network structure.]

Notes. In the network on the left, λ = (2, 2, 2, 1/2, 1/3, 1/4), μ = (1, 1, 1, 1/2, 1/3, 1/4), r = (1, 1, 1, 4, 6, 8), and q = (2, 2, 2). The LP solution satisfies Assumption 3.1, and the less-preferred types are 1, 2, and 3. The lp-residual graph has two perfect matchings: {(1, a), (2, b), (3, c)} and {(1, b), (2, c), (3, a)}. On the right is an expanded network with the addition of a type 7 and a resource d that have λ<sub>7</sub> = μ<sub>7</sub> = 1, r<sub>7</sub> = 10, q<sub>d</sub> = 2; all other parameters remain the same. In this network, there is no perfect matching. The LP for this network has a degenerate LP solution in which the slack variable for the binding resource d is zero. For visibility, we draw the preferred types at the bottom of the graph.

is a nonsingular matrix by Lemma 3.2. Similarly, let A<sub>p</sub> be the matrix with the columns corresponding to the preferred types. The residual resource j capacity, after allocation to the preferred types, is q<sup>N</sup><sub>j</sub> - ∑<sub>i∈A<sub>p</sub></sub> A<sub>ji</sub>λ<sup>N</sup><sub>i</sub>/μ<sub>i</sub> or, in vector form, q<sup>N</sup> - A<sub>p</sub>(λ<sup>N</sup>/μ)<sub>A<sub>p</sub></sub>. The optimal deterministic allocation to the less-preferred types is then

$$Ny^*_{A_{lp}} = A^{-1}_{lp}(q^N - A_p(\lambda^N/\mu)_{A_p}).$$  (2)

The solution to (LP) is nondegenerate so that, for a small perturbation ζ of (λ<sup>N</sup>/μ)<sub>A<sub>p</sub></sub>, we have the same optimal basis, and in turn,

$$x^*(ζ) := A^{-1}_{lp}(q^N - A_p ζ)$$  (3)

is the optimal allocation of residual capacity among less-preferred types given a load ζ from the preferred types. Using (2), we write this target allocation as

$$x^*(ζ) = y^*_{A_{lp}} N + A^{-1}_{lp} A_p[(\lambda^N/\mu)_{A_p} - ζ];$$  (4)

because |A<sub>lp</sub>| = d, x<sup>*</sup>(ζ) is a d-dimensional vector.

We use x<sup>*</sup>(ζ) as a definition regardless of whether ζ is a sufficiently small perturbation of (λ<sup>N</sup>/μ)<sub>A<sub>p</sub></sub>.

## 4. The CHT Policy and the Regret Bound

For each i ∈ [n], we use X<sup>t</sup><sub>i</sub> to denote the number of type i customers occupying resource unit(s) at time t. We let

$$\Sigma^t_j = \sum_{i \in A(j)} X^t_i = \sum_{i \in A} A_{ji} X^t_i,$$

(resource j head count)

be the number of customers that are occupying resource j units at time t.

Our policy uses, dynamically, the target allocation levels (3). At time t, and with X<sup>t</sup><sub>A<sub>p</sub></sub> being the real-time allocation to preferred types, the target allocation to less-preferred type i is

$$X^{*,t}_i := x^*_i(X^t_{A_p});$$  (5)

it is a random quantity that depends on the real-time count of preferred-type customers in service.

Recall that i<sub>j</sub> ∈ [n] is the customer type matched to resource j ∈ [d] under the chosen perfect matching. The CHT policy (see Algorithm 1) is applied to corrected head count processes

$$\Sigma^{*,t}_j := \Sigma^t_j + \sum_{i \in A_{lp} \setminus i_j} A_{ji}(X^{*,t}_i - X^t_i),$$

where the correction process

$$Z^{*,t}_j = \sum_{i \in A_{lp} \setminus i_j} A_{ji}(X^{*,t}_i - X^t_i),$$

captures the difference between targeted occupancy levels and actual levels for less preferred types.

A threshold on the corrected head count process at resource j<sub>i</sub> (the resource matched with i ∈ A<sub>lp</sub>) determines whether a type i request can be accepted or not. Thresholds are placed only on the edges (i, j<sub>i</sub>) for i ∈ A<sub>lp</sub>; these are the edges marked with ∥ in Figures 2 and 3. There is one threshold per i ∈ A<sub>lp</sub> and, hence, by Lemma 3.2, a total of d = |A<sub>lp</sub>| thresholds.

**Algorithm 1 (CHT)**

Require: Threshold constants δ<sub>i</sub> for every less-preferred customer type i ∈ A<sub>lp</sub>.

1: Accept an arriving request of type i ∈ A<sub>p</sub> (preferred types) whenever feasible.

2: Accept an arriving type i ∈ A<sub>lp</sub> at time t if and only if it is feasible (Σ<sup>t</sup><sub>j</sub> < q<sup>N</sup><sub>j</sub> for all j ∈ S(i)) and there are—in terms of the corrected head count—more than R<sub>i</sub> := δ<sub>i</sub> log N units of resource j<sub>i</sub> available:

$$q^N_{j_i} - \Sigma^{*,t}_{j_i} \geq R_i = \delta_i \log N.$$

Conceptually, the use of the corrected head count process helps network alignment. Resource j<sub>i</sub> is more conservative in accepting type i if types k ∈ A<sub>lp</sub>(j<sub>i</sub>)\i are significantly below their targeted levels and, in doing so, is reserving capacity for their arrivals.

The vector of threshold coefficients δ = (δ<sub>1</sub>, ..., δ<sub>d</sub>) in Algorithm 1 is not arbitrary. These must be chosen to be sufficiently large (but independent of N). We provide some guidance on the choice of thresholds in Appendix B.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                2105

## Figure 5. (Color online) Policy Implementation for Example 3.1

[Network diagram showing nodes 1, 2, 3 connected to resources a and b, where node 1 connects to both a and b, node 2 connects to a, and node 3 connects to b. Nodes 2 and 3 are highlighted in red.]

Accept a request of type
* 1 when feasible
* 2 when feasible and $q_a - \Sigma_a^{*,t} \geq \delta_2 \log N$ with
  
  $\Sigma_a^{*,t} = \Sigma_a^t = X_1^t + X_2^t$

* 3 when feasible and $q_b - \Sigma_b^{*,t} \geq \delta_3 \log N$ where
  
  $\Sigma_b^{*,t} = \Sigma_b^t + X_2^{*,t} - X_2^t = X_1^t + X_3^{*,t}$

The target allocation for type 2 is $X_2^{*,t} = q_a^N - X_1^t$.

Given the identification of the threshold placement, a more natural threshold policy is one that uses the head count $\Sigma_j^t$ instead of its corrected counterpart $\Sigma_j^{*,t}$, but the corrected head count has significant mathematical benefits. Except $X_i^t$, all less-preferred types requiring $j$ appear in the corrected head count only through their targeted amounts. We further discuss the mathematical implications of the policy design in Section 5 and compare CHT and the natural threshold policy in our numerical experiments in Section 7; see also Example 4.1.

**Example 4.1.** Let us spell out the policy implementation in Examples 3.1–3.3. In Example 3.1, the unique perfect matching is $\{(2, a), (3, b)\}$. The policy is spelled out in Figure 5. In Example 3.2, the unique perfect matching is $\{(1, d), (2, a), (3, c), (5, b)\}$, and Figure 6 spells out the policy for this network. In Example 3.3, there

are two perfect matchings; we use the perfect matching $\{(1, a), (2, b), (3, c)\}$, and the policy is spelled out in Figure 7.

It is useful to use the simple Example 3.1 to highlight a benefit of the corrected head count process. Suppose that, at a time $t$, $X_2^t + X_3^t \leq q_b^N - \delta_3 \log N$, but $X_2^t < X_2^{*,t}$ and $X_2^{*,t} + X_3^t > \delta_3 \log N$. In this state, the standard threshold policy, acting on the true head count, accepts type 3 requests. CHT, in contrast, is more conservative in accepting type 3, effectively reserving capacity for type 2 so that it can be brought back to its targeted level $X_2^{*,t}$. Whereas the regular threshold policy might eventually correct itself, CHT acts forcefully to align allocation levels with their targeted values.

Under CHT, $X^t \in \mathbb{Z}_+^n$ forms an $n$-dimensional continuous-time Markov chain with a finite state space. It is easy to check that the chain is irreducible<sup>1</sup> and, as a

## Figure 6. (Color online) Policy Implementation for Example 3.2

[Network diagram showing nodes 1, 2, 3, 4, 5, 6, 7 connected to resources a, b, c, d. Nodes 1, 2, 3, 5 are highlighted in red. The connections are: node 1 connects to a and b, node 2 connects to a and b, node 3 connects to b and c, node 4 connects to c, node 5 connects to b, node 6 connects to c and d, node 7 connects to d.]

Accept a request of type
* 4, 6, 7 when feasible
* 1 when feasible and $q_a - \Sigma_a^{*,t} \geq \delta_1 \log N$ with
  
  $\Sigma_a^{*,t} = \Sigma_a^t = X_2^t + X_1^t$

* 2 when feasible and $q_a - \Sigma_a^{*,t} \geq \delta_2 \log N$ where
  
  $\Sigma_a^{*,t} = \Sigma_a^t = X_1^t + X_2^t$

* 3 when feasible and $q_c - \Sigma_c^{*,t} \geq \delta_3 \log N$ where
  
  $\Sigma_c^{*,t} = \Sigma_c^t + X_1^{*,t} - X_1^t = X_6^t + X_4^t + X_1^{*,t}$

* 5 when feasible and $q_b - \Sigma_b^{*,t} \geq \delta_5 \log N$ where
  
  $\Sigma_b^{*,t} = \Sigma_b^t + \sum_{i=1}^3 (X_i^{*,t} - X_i^t) = X_2^t + \sum_{i=1}^3 X_i^{*,t}$

The target allocations for types 1, 2, and 3 are given by

$X_1^{*,t} = q_a^N - X_2^t$, $X_2^{*,t} = q_a - X_1^t$, and $X_3^{*,t} = q_c^N - X_6^t - X_1^{*,t}$.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2106                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

## Figure 7. (Color online) Policy Implementation for Example 3.3

```mermaid
graph TD
    1((1)) --> a[a]
    2((2)) --> a[a]
    2((2)) --> b[b]
    3((3)) --> b[b]
    3((3)) --> c[c]
    a[a] --> 4((4))
    b[b] --> 5((5))
    c[c] --> 6((6))
```

Accept a request of type
* 4, 5, 6 when feasible
* 1 when feasible and q<sub>a</sub> - Σ<sub>a</sub><sup>*,t</sup> ≥ δ<sub>1</sub> log N with

$$\Sigma_a^{*,t} = X_1^t + X_3^{*,t} + X_4^t$$

* 2 when feasible and q<sub>b</sub> - Σ<sub>b</sub><sup>*,t</sup> ≥ δ<sub>2</sub> log N where

$$\Sigma_b^{*,t} = X_2^t + X_1^{*,t} + X_5^t$$

* 3 when feasible and q<sub>c</sub> - Σ<sub>c</sub><sup>*,t</sup> ≥ δ<sub>3</sub> log N where

$$\Sigma_c^{*,t} = X_3^t + X_2^{*,t} + X_6^t$$

The target allocations for types 1, 2, and 3 are given by

$$X_1^{*,t} = \frac{1}{2}(q_a^N + q_b^N - q_c^N) - \frac{1}{2}(X_4^t + X_5^t - X_6^t),$$

$$X_2^{*,t} = \frac{1}{2}(-q_a^N + q_b^N + q_c^N) - \frac{1}{2}(-X_4^t + X_5^t + X_6^t), \text{ and}$$

$$X_3^{*,t} = \frac{1}{2}(q_a^N - q_b^N + q_c^N) - \frac{1}{2}(X_4^t - X_5^t + X_6^t).$$

consequence, conclude that there exists a unique stationary distribution Π, which is also the steady-state distribution. When considering stationary variables, we omit the time superscript t. By Little's law, the number of type i requests accepted per unit of time in stationarity equals μ<sub>i</sub>E<sub>Π</sub>[X<sub>i</sub>], so the performance of CHT is R<sup>CHT</sup> = r'<sub>μ</sub>E<sub>Π</sub>[X] = Σ<sub>i</sub>r<sub>i</sub>μ<sub>i</sub>E<sub>Π</sub>[X<sub>i</sub>].

## Theorem 4.1 (Main Result). 
Suppose that Assumption 3.1 holds. Let Π be the stationary distribution induced by CHT. Then,

$$R^{*,N} - r'_μE_Π[X] ≤ \mathcal{R}(q^N, λ^N/μ) - r'_μE_Π[X] = O(\log N).$$

Moreover, for any network that satisfies Assumption 3.1 with A<sub>p</sub> ≠ ∅ and any family of admissible policies (π<sup>N</sup>, N ∈ ℕ<sub>+</sub>),

$$\mathcal{R}(q^N, λ^N/μ) - R^{π^N,N} = Ω(\log N).$$

### Remark 4.1 (The Case d = 1). 
If there is a single resource, then by Lemma 3.2, |A<sub>lp</sub>| = 1 (a single less-preferred type). In this case, the corrected head count process is the same as the head count itself: Σ<sup>*,t</sup> = Σ<sup>t</sup> = Σ<sub>i∈A<sub>p</sub></sub>X<sub>i</sub><sup>t</sup> + X<sub>i<sub>0</sub></sub><sup>t</sup>, where i<sub>0</sub> is the unique less-preferred type. Our policy reduces in this case to the simple threshold policy:

1. Accept any request of types i ∈ A<sub>p</sub> whenever there are units of the resource available.
2. Accept a request of type i<sub>0</sub> arriving at time t if q<sup>N</sup> - Σ<sup>t</sup> ≥ δ<sub>i</sub> log N.

It follows from Theorem 4.1 that the approximation gap of standard trunk reservation with a logarithmic threshold is logarithmic in N. For the special case with equal service rates (μ<sub>i</sub> ≡ μ), the logarithmic lower bound is implied by the literature. Reiman (1991) and Morrison (2010) prove that the optimal policy's threshold for the class with the smallest reward is logarithmic in N. It then follows, from arguments similar to our proofs, that the approximation gap of the optimal policy in the overloaded regime is logarithmic in N; no policy can do better than the optimal one. In this paper, we prove that this lower bound is generally true in overloaded networks.

### Remark 4.2 (Why a Logarithmic Threshold?). 
Having the thresholds scale logarithmically in N is the sufficient (and necessary) capacity reservation to protect the preferred type requests. For intuition, it suffices to consider the case of a single resource (d = 1) with a common service rate across types (μ<sub>i</sub> ≡ μ). When the number of occupied servers is greater q<sup>N</sup> - R, only customers of types A<sub>p</sub> are accepted. In these states, the head count increases at rate Σ<sub>i∈A<sub>p</sub></sub>λ<sub>i</sub><sup>N</sup> and, because almost all servers are busy, decreases at a rate ≈ μq<sup>N</sup>. Approximately, then, the head count behaves in these states like an M/M/1 queue with utilization:

$$ρ^- := \frac{\sum_{i∈A_p}λ_i^N}{μq^N} < 1.$$

It is the nondegeneracy assumption that guarantees ρ<sup>-</sup> < 1. The likelihood that all servers are busy—at
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                               2107

which point preferred customers are rejected—is roughly ρ<sup>R</sup>. Choosing R to be of the order of log N guarantees that this probability is of the order of 1/N. A threshold that is an order of magnitude smaller would not suffice; a constant threshold (i.e., one that does not scale with N), in particular, would result in a nonnegligible fraction of preferred customers being rejected.

**Remark 4.3** (The Lower Bound When A<sub>p</sub> = ∅). For the lower bound in Theorem 4.1, we assume that A<sub>p</sub> ≠ ∅. This is generally necessary. To see this, consider a single resource (d = 1) and suppose that types are labeled in decreasing order of r<sub>i</sub>μ<sub>i</sub>: r<sub>1</sub>μ<sub>1</sub> > r<sub>2</sub>μ<sub>2</sub>... > r<sub>n</sub>μ<sub>n</sub>. Recalling the structure of the (LP) solution in Example 2.1, we note that, if A<sub>p</sub> = ∅, then y<sub>1</sub><sup>*</sup> = q < λ<sub>1</sub>/μ<sub>1</sub>, y<sub>i</sub><sup>*</sup> = 0 for all i ≠ 1, and R(q, λ/μ) = r<sub>1</sub>μ<sub>1</sub>q.

The policy that accepts a type 1 request whenever there is capacity available and rejects all other requests achieves constant regret. Indeed, under this policy, X<sub>1</sub><sup>t</sup> has the law of a single-class M/M/q<sup>N</sup>/q<sup>N</sup> queue with arrival rates λ<sub>1</sub><sup>N</sup>, service rate μ<sub>1</sub>, and load ρ = λ<sub>1</sub><sup>N</sup>/(μ<sub>1</sub>q<sup>N</sup>) = λ<sub>1</sub>/(μ<sub>1</sub>q) > 1. It is a simple fact that this overloaded single-class loss queue has E<sub>Π</sub>[q<sup>N</sup> - X<sub>1</sub>] = O(1), where Π is the steady-state distribution. In turn, r<sub>1</sub>μ<sub>1</sub>E<sub>Π</sub>[X<sub>1</sub>] = r<sub>1</sub>μ<sub>1</sub>q<sup>N</sup> + O(1) = R(q<sup>N</sup>, λ<sup>N</sup>/μ) + O(1) = R(q<sup>N</sup>, λ<sup>N</sup>/μ) + o(log N).

**Remark 4.4** (State Dependence in the Policy Is Necessary). Our proposed policy is state dependent, meaning that the decision to accept or reject a request depends on the state of the system (beyond the mere availability of resource units). In Appendix C, we prove that state dependence is necessary: there is no static randomization policy that achieves the logarithmic regret.

## 5. Proof of the Upper Bound in Theorem 4.1

Recall that R(q<sup>N</sup>, λ<sup>N</sup>/μ) = r'<sub>μ</sub>Ny<sup>*</sup> and R<sup>CHT</sup> = r'<sub>μ</sub>E<sub>Π</sub>[X], so that

R(q<sup>N</sup>, λ<sup>N</sup>/μ) - R<sup>CHT</sup> = r'<sub>μ</sub>(Ny<sup>*</sup> - E<sub>Π</sub>[X]),

where E<sub>Π</sub>[X] = (E<sub>Π</sub>[X<sub>1</sub>], ..., E<sub>Π</sub>[X<sub>n</sub>])'. Accordingly, we focus our analysis on bounding the gap Ny<sup>*</sup> - E<sub>Π</sub>[X<sub>i</sub>].

The vector E<sub>Π</sub>[X] is a nontrivial entity to study directly because of the dependencies between customer types. In the network of Figure 3, whether a type 3 request can be accepted depends on both the number of available units of resource c and the number of available units of resource b, which is, in turn, affected by the number of types 1 and 2 customers in the system.

Instead of studying X<sup>t</sup> directly, we introduce a network in which the resource constraints are relaxed in a way that facilitates analysis but, at the same time, has a provably close performance to that of CHT in the original network.

### 5.1. A Network with Relaxed Constraints

The network, operated by CHT, has the state space

X = X<sup>N</sup> ≡ {x ∈ Z<sup>n</sup><sub>+</sub> : Ax ≤ q<sup>N</sup>} ∩ {x ∈ Z<sup>n</sup><sub>+</sub> : x<sub>i</sub> ≤ q<sup>N</sup><sub>j<sub>i</sub></sub> - δ<sub>i</sub> log N + 1, for all i ∈ A<sub>lp</sub>}.
(6)

+Because no type i (i ∈ A<sub>lp</sub>) requests are accepted when more than q<sup>N</sup><sub>j<sub>i</sub></sub> - δ<sub>i</sub> log N units of resource j<sub>i</sub> are occupied, there can be no more than this number of these in the system; this is captured by the second set in the intersection that defines X. In the auxiliary network, we remove all but these constraints; it has the state space

X̃ = X̃<sup>N</sup> ≡ {x ∈ Z<sup>n</sup><sub>+</sub> : x<sub>i</sub> ≤ q<sup>N</sup><sub>j<sub>i</sub></sub> - δ<sub>i</sub> log N + 1 for all i ∈ A<sub>lp</sub>}.
(7)

In the auxiliary network, there is an infinite number of units of each resource, and access of types i ∈ A<sub>lp</sub> is restricted only by the threshold on the head count of resource j<sub>i</sub> (and not by the occupancy of any other resource j ≠ j<sub>i</sub>). Access to types i ∈ A<sub>p</sub> is not restricted at all; see Algorithm 2. This localizes admission decisions: whether we accept a type i request depends only on the corrected head count of resource j<sub>i</sub>, not that of any other resource.

**Auxiliary-Network Notation.** We superscript with ~ processes associated with the auxiliary network: X̃<sub>i</sub><sup>t</sup> counts the number of type i customers in the relaxed network under the policy π̃ in Algorithm 2, Σ̃<sub>j</sub><sup>t</sup> is the resource j head count process in the auxiliary network, Z̃<sub>j</sub><sup>*,t</sup> is the correction process, and Σ̃<sub>j</sub><sup>*,t</sup> is the corrected head count process. We use Π̃ for the steady-state distribution in the auxiliary network using the policy π̃; Π̃(B) for B ⊆ X̃ is the probability that this distribution assigns to the set B of states. The existence of this distribution is established in Proposition 5.1.

**Algorithm 2** (The Policy π̃ in the Relaxed Network)
Require: Threshold constants δ<sub>i</sub> for every less-preferred customer type i ∈ A<sub>lp</sub> (same as those of CHT).
1: Accept all preferred customer types i ∈ A<sub>p</sub> (even if it violates any resource constraint).
2: Accept an arriving type i ∈ A<sub>lp</sub> if and only if the corrected head count, Σ̃<sub>j<sub>i</sub></sub><sup>*,t</sup> = Σ̃<sub>j<sub>i</sub></sub><sup>t</sup> + Z̃<sub>j<sub>i</sub></sub><sup>*,t</sup>, with resource j<sub>i</sub> has

q<sup>N</sup><sub>j<sub>i</sub></sub> - Σ̃<sub>j<sub>i</sub></sub><sup>*,t</sup> ≥ R<sub>i</sub> = δ<sub>i</sub> log N.

**How the Relaxation Supports the Analysis of CHT.** In Example 4.1, we illustrate how CHT, in contrast to the regular threshold policy (one that acts directly on the true rather than corrected head count), acts forcefully to bring the allocations toward their optimally targeted levels. The mathematical benefit of the corrected head count becomes evident in the relaxed network.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2108                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

Take the network in Figure 3 and recall the policy implementation as spelled out in Figure 6. In the relaxed network, requests of preferred types, in particular, of type 7, face no resource constraints; all are accepted, and in turn, their number in system evolves as an infinite server queue and is straightforward to analyze; the same is true for type 6. Consider now type 3 requests; these are matched with resource c (j<sub>i</sub> = c), and we note two things: (i) in the auxiliary network, their acceptance/rejection does not depend on the real-time head count of resource b, which they also require, and (ii) the corrected head count of their matched resource c is given by Σ̃<sub>c</sub> = X̃<sub>3</sub><sup>t</sup> + X̃<sub>6</sub><sup>t</sup> + X̃<sub>1</sub><sup>*,t</sup>. Here, less-preferred type 1 appears only through its targeted value X̃<sub>1</sub><sup>*</sup> = q<sub>d</sub><sup>N</sup> - X̃<sub>7</sub><sup>t</sup>, where X̃<sub>7</sub><sup>t</sup>, as we just explained, is straightforward to analyze; so is X̃<sub>6</sub><sup>t</sup>.

In this way, the analysis is localized to each pair of resource j and its coupled request type i<sub>j</sub>. Through this localized analysis, we show that, in the relaxed network,

$$(AX̃)_j = Σ̃_j ≈ Σ̃_j^* ≈ q_j^N - δ_{ij} \log N ≤ q_j^N,$$

so that, whereas the auxiliary network has the large state space X̃, it effectively remains within the smaller state space X of the original network. Because the policies CHT and π̃ take the same actions in states within X, the auxiliary network and policy—though easier to analyze—capture (approximately) the behavior of the original network and CHT.

Crucially, the auxiliary network is hardly a relaxation from a performance perspective. The reward collected by CHT in the original network and of π̃ in the relaxed network are, as we prove, close to each other. In this way, we are able to bound the performance of CHT in the network by analyzing a simpler one.

## 5.2. Proof Steps

**Step 1.** In the relaxed network, the gap between the performance of π̃ and the optimal value in (LP) is logarithmic in N. That is,

$$r'_μ E_Π̃[X̃] = R(q^N, λ^N/μ) + O(\log N).$$

**Step 2.** In the original network, the Markov chain X<sup>t</sup>, induced by CHT, has a 1/N-mixing time that is polynomial in N. This implies that, for all t greater than a time t<sub>0</sub> = t<sub>0</sub>(N) that is polynomial in N, and regardless of the initial (at t = 0) state x, the distribution of X<sup>t</sup> is close to the steady-state distribution Π,

$$r'_μ E_x[X^{t_0}] = r'_μ E_Π[X] + O(1), \text{ for all } t ≥ t_0.$$

**Step 3.** Late decoupling: X<sup>t</sup> and X̃<sup>t</sup>, suitably initialized (at t = 0)<sup>2</sup> with the stationary distribution Π̃ induced by π̃, decouple later than the mixing time of

X<sup>t</sup>. That is, with high probability, X<sup>t</sup> = X̃<sup>t</sup> until a time t<sub>1</sub> > t<sub>0</sub> (with t<sub>0</sub> as in the previous item).

At time t<sub>0</sub>—when X<sup>t</sup> is close to its stationary distribution Π—it is also equal to X̃<sup>t</sup>, which has (and is initialized at time t = 0 with) the stationary distribution Π̃ induced by π̃. This implies that the two stationary distributions—of CHT and of π̃ are close—and so are their collected rewards:

$$R^{CHT} = r'_μ E_Π[X] \stackrel{\text{Step 2}}{≈} r'_μ E_Π[X^{t_0}]$$

$$\stackrel{\text{Step 3}}{≈} r'_μ E_Π̃[X̃^{t_0}] \stackrel{\text{Stationarity}}{=} r'_μ E_Π̃[X̃]$$

$$\stackrel{\text{Step 1}}{≈} R(q^N, λ^N/μ) + O(\log N).$$

Sections 5.3–5.5 formalize steps 1–3. These are combined in Section 5.6 to obtain the upper bound in Theorem 4.1.

Throughout the proofs, we use η<sub>l</sub>, l = 1, 2, ... to denote strictly positive constants that do not depend on N. The exact values of these constants might change from one proof to the next.

## 5.3. Step 1: The Performance of the Relaxed Policy p̃

We use symbols such as X̃, Σ̃ to denote quantities associated with the relaxed network and the relaxed policy π̃. Recall that X and X̃ are the state space of CHT and the relaxed policy π̃, respectively, in (6) and (7).

Proposition 5.1 shows that the stationary reward collected by the relaxed policy π̃ is logarithmically close to the optimal value R from (LP). The second statement of the proposition stipulates that, allowing for a larger state space, the process X̃<sup>t</sup> spends most of its time in the smaller state space X of X<sup>t</sup>.

In what follows, we use the notation

$$X̃\backslash X := X̃\backslash X ∪ \{x ∈ X̃ : (Ax)_j = q_j^N, \text{ for some } j ∈ [d]\}.$$

**Proposition 5.1** (Performance of π̃). The Markov chain X̃<sup>t</sup> has a unique stationary distribution Π̃ that satisfies

$$r'_μ E_Π̃[X̃] = R(q^N, λ^N/μ) + O(\log N) \text{ and}$$

$$Π̃(X̃\backslash X) = O(N^{-mδ_{min}}),$$

where δ<sub>min</sub> = min<sub>i</sub> δ<sub>i</sub> and m > 0 does not depend on δ and N.

We use two preliminary lemmas in the proof of Proposition 5.1. We start with the observation that, for i ∈ A<sub>p</sub>, X̃<sub>i</sub><sup>t</sup> has the law of an M/M/∞ queue so that, in particular, its stationary distribution is Poisson with mean λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>; the probability bound in the next lemma follows from standard concentration results for Poisson random variables.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                2109

**Lemma 5.1 (Concentration of Preferred Types).** The Markov chain X̃<sup>t</sup> has a unique stationary distribution Π̃, and for every fixed ε > 0,

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{X} \notin \Omega_\varepsilon^N\} \leq m_1 e^{-m_2 N}, \text{ and}$$

$$\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i] = \lambda_i^N/\mu_i, \text{ for } i \in \mathcal{A}_p,$$

where

$$\Omega_\varepsilon^N := \{x \in \tilde{X} : |x_i - \lambda_i^N/\mu_i| \leq \varepsilon N \text{ for all } i \in \mathcal{A}_p\}, \quad (8)$$

and m<sub>1</sub>, m<sub>2</sub> > 0 may depend on ε but not on N.

The (random) target allocations (X̃<sub>i</sub><sup>*</sup>, i ∈ A<sub>lp</sub>), defined in terms of X̃<sub>A<sub>p</sub></sub>, inherit properties from this lemma. By construction, X̃<sup>*</sup> = y<sub>A<sub>lp</sub></sub><sup>*</sup>N + A<sub>lp</sub><sup>-1</sup>A<sub>p</sub>[(λ<sup>N</sup>/μ)<sub>A<sub>p</sub></sub> - X̃<sub>A<sub>p</sub></sub>], so by Lemma 5.1,

$$\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i^*] = y_i^* N. \quad (9)$$

For each j ∈ [d] and x ∈ ℝ<sup>n</sup>, define the function

$$\sigma_j(x) = \sum_{k \in \mathcal{A}(j)} x_k + \sum_{i \in \mathcal{A}_{lp} \backslash i_j} A_{ji}(x_i^* - x_i)$$

$$= x_{i_j} + \sum_{l \in \mathcal{A}_p(j)} x_l + \sum_{l \in \mathcal{A}_{lp}(j) \backslash i_j} x_l^*, \quad (10)$$

where we recall x<sup>*</sup> = x<sub>A<sub>lp</sub></sub><sup>*</sup>(x) = y<sub>A<sub>lp</sub></sub><sup>*</sup>N + A<sub>lp</sub><sup>-1</sup>A<sub>p</sub>[(λ<sup>N</sup>/μ)<sub>A<sub>p</sub></sub> - x<sub>A<sub>p</sub></sub>]; see Equation (4). In particular, Σ<sub>j</sub><sup>*,t</sup> = σ<sub>j</sub>(X̃<sup>t</sup>). We define also the functions

$$f_i(x) = |\sigma_{j_i}(x) - \hat{q}_{j_i}^N|, \text{ and } g_i^θ(x) = \exp(θf_i(x)), \quad i ∈ \mathcal{A}_{lp}, \quad (11)$$

where we use the shorthand notation

$$\hat{q}_{j_i}^N = q_{j_i}^N - δ_{ij} \log N.$$

**Lemma 5.2.** There exists θ > 0 (not depending on N) such that, for all i ∈ A<sub>lp</sub>,

$$\sum_{x \in \tilde{X}} \tilde{\Pi}(x)|Q(x,x)||g_i^θ(x)| < \infty,$$

where Q is the transition rate matrix of the Markov chain X̃<sup>t</sup>. In turn,

$$\mathbb{E}_{\tilde{\Pi}}[(Qg_i^θ)(\tilde{X})] = 0.$$

**Lemma 5.3.** Under the assumptions of Theorem 4.1, there exist θ, m<sub>1</sub>, m<sub>2</sub> > 0 (that do not depend on N but may depend on ε) such that

$$(Qg_i^θ)(x) \leq -m_1 Ng_i^θ(x) + m_2 N, \text{ for all } x \in \Omega_\varepsilon^N.$$

Lemma 5.3 shows that the process g(X̃<sup>t</sup>) has a centering drift property; when g(X̃<sup>t</sup>) is large (meaning σ<sub>j<sub>i</sub></sub>(X̃<sup>t</sup>) is far from q̂<sub>j<sub>i</sub></sub><sup>N</sup>), it decreases in expectation and (σ<sub>j<sub>i</sub></sub>(X̃<sup>t</sup>) is pushed back toward q̂<sub>j<sub>i</sub></sub><sup>N</sup>).

**Proof of Proposition 5.1.** We first show that, for each i ∈ A<sub>lp</sub>,

$$\mathbb{P}_{\tilde{\Pi}}\{|\tilde{X}_i^* - \tilde{X}_i| \geq δ_i \log N + x\} \leq η_1 e^{-η_2 x}, \text{ and}$$

$$\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i] = y_i^* N + O(\log N). \quad (12)$$

Fix i ∈ A<sub>lp</sub>, θ > 0 and let f(x) and g(x) (with the corresponding subscript/superscript dropped) be as in (11) for this fixed i, θ. Recall that Q stands for the infinitesimal generator (the transition rate matrix) of the n-dimensional process X̃<sup>t</sup>. By Lemma 5.2,

$$0 = \mathbb{E}_{\tilde{\Pi}}[(Qg)(\tilde{X})] = \mathbb{E}_{\tilde{\Pi}}[(Qg)(X)1\{\tilde{X} \in \Omega_\varepsilon^N\}]$$

$$+ \mathbb{E}_{\tilde{\Pi}}[(Qg)(\tilde{X})1\{\tilde{X} \notin \Omega_\varepsilon^N\}]. \quad (13)$$

Lemma 5.3 immediately implies that

$$\mathbb{E}_{\tilde{\Pi}}[(Qg)(\tilde{X})1\{\tilde{X} \in \Omega_\varepsilon^N\}]$$

$$\leq -m_1 N\mathbb{E}_{\tilde{\Pi}}[g(\tilde{X})1\{\tilde{X} \in \Omega_\varepsilon^N\}] + m_2 N, \quad (14)$$

and we turn to consider the second term on the right-hand side of (13). By Hölder's inequality,

$$\mathbb{E}_{\tilde{\Pi}}[|(Qg)(\tilde{X})|1\{\tilde{X} \notin \Omega_\varepsilon^N\}]$$

$$\leq \sqrt{\mathbb{E}_{\tilde{\Pi}}[((Qg)(\tilde{X}))^2]} \sqrt{\mathbb{P}_{\tilde{\Pi}}\{\tilde{X} \notin \Omega_\varepsilon^N\}}.$$

The transition rate matrix satisfies |Q(x,y)| ≤ ∑<sub>l∈[n]</sub> (λ<sub>l</sub><sup>N</sup> + μ<sub>l</sub>x<sub>l</sub>) and Q(x,y) ≠ 0 only for y = x or |y - x| = e<sub>l</sub> for some l ∈ [n]. Recalling (10) and that x<sub>l</sub><sup>*</sup>(ζ) = y<sub>l</sub><sup>*</sup>N + ∑<sub>k∈A<sub>p</sub></sub> α<sub>l</sub><sup>k</sup>(ζ<sub>k</sub> - λ<sub>k</sub><sup>N</sup>/μ<sub>k</sub>), where α<sub>l</sub><sup>k</sup> = [A<sub>lp</sub><sup>-1</sup>A<sub>p</sub>]<sub>l,k</sub>, i ∈ A<sub>lp</sub>, k ∈ A<sub>p</sub>, we have that

$$\sigma_{j_i}(x) \leq dq_{\max}N + (1 \vee \alpha) \sum_{k \in A_p} x_k, \text{ and}$$

$$\sigma_{j_i}(x + e_l) - \sigma_{j_i}(x)| \leq 1 \vee \alpha,$$

where α = max<sub>i,l</sub>|α<sub>i,l</sub>|, and we use the fact that x<sub>i</sub> ≤ q<sub>max</sub>N for all i ∈ A<sub>lp</sub>, with q<sub>max</sub> := max<sub>j</sub>q<sub>j</sub>. Combining these, we have that

$$|(Qg)(x)| \leq \sum_y |Q(x,y)||g(y)|$$

$$\leq e^{θ(\sigma_{j_i}(x)+1\vee\alpha)} \sum_{l∈[n]} (\lambda_l^N + μ_l x_l)$$

$$\leq η_3 e^{θ(dq_{\max}N+(1\vee\alpha)\sum_{l∈A_p}x_l+1\vee\alpha)} \left(N + \sum_{l∈A_p} x_l\right).$$

In particular,

$$((Qg)(x))^2$$

$$\leq η_4 e^{2θ(dq_{\max}N+(1\vee\alpha)\sum_{l∈A_p}x_l+1\vee\alpha)} \left(N^2 + \sum_{l∈A_p} x_l^2\right).$$

We recall that, under the steady-state distribution Π̃, X̃<sub>l</sub> ~ Poisson(λ<sub>l</sub><sup>N</sup>/μ<sub>l</sub>), l ∈ A<sub>p</sub> (and X̃<sub>l</sub>, l ∈ A<sub>p</sub> are independent). Using Hölder's inequality, we have, for θ
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2110                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

small enough (not dependent on N),

$$\mathbb{E}_{\tilde{\Pi}}\left[\sum_{l \in A_p}(\tilde{X}_l)^2 e^{2\theta \sum_{k \in A_p} X_k}\right] \leq \eta_4 N^2 e^{2\theta \kappa N},$$

where $\kappa = \sum_{l \in A_p} \frac{\lambda_l}{\mu_l}$ (recall $\lambda_l^N = \lambda_l N$). Choosing $\theta$ such that $2\theta \kappa \leq m_2/2$ (with $m_2$ as in Lemma 5.1), we have

$$\mathbb{E}_{\tilde{\Pi}}\left[|(Q_g)(\tilde{X})| \mathbf{1}\{\tilde{X} \notin \Omega_\varepsilon^N\}\right]$$

$$\leq \sqrt{\mathbb{E}_{\tilde{\Pi}}\left[((Q_g)(\tilde{X}))^2\right]} \sqrt{\mathbb{P}_{\tilde{\Pi}}\{\tilde{X} \notin \Omega_\varepsilon^N\}} \leq \eta_5. \quad (15)$$

Plugging (14) and (15) into (13), we have that

$$-\eta_5 \leq \mathbb{E}_{\tilde{\Pi}}\left[(Q_g)(\tilde{X}) \mathbf{1}\{\tilde{X} \in \Omega_\varepsilon^N\}\right]$$

$$\leq -\eta_6 N \mathbb{E}_{\tilde{\Pi}}\left[g(\tilde{X}) \mathbf{1}\{\tilde{X} \in \Omega_\varepsilon^N\}\right] + \eta_7 N,$$

so that

$$\mathbb{E}_{\tilde{\Pi}}\left[g(\tilde{X}) \mathbf{1}\{\tilde{X} \in \Omega_\varepsilon^N\}\right] \leq \frac{\eta_5 + \eta_7 N}{\eta_6 N} \leq \eta_8 = O(1).$$

Recall that $g(x) = e^{\theta|\sigma_{j_i}(x) - \hat{q}_{j_i}^N|} \leq e^{\theta(\sigma_{j_i}(x) + \hat{q}_{j_i} N)}$. Following a similar argument to those leading to (15), we can conclude that $\mathbb{E}_{\tilde{\Pi}}[g(\tilde{X}) \mathbf{1}\{\tilde{X} \notin \Omega_\varepsilon^N\}] \leq \eta_9$ so that

$$\mathbb{E}_{\tilde{\Pi}}[g(\tilde{X})] = \mathbb{E}_{\tilde{\Pi}}[g(\tilde{X}) \mathbf{1}\{\tilde{X} \in \Omega_\varepsilon^N\}]$$

$$+ \mathbb{E}_{\tilde{\Pi}}[g(\tilde{X}) \mathbf{1}\{\tilde{X} \notin \Omega_\varepsilon^N\}] \leq \eta_{10}.$$

By Markov's inequality

$$\mathbb{P}_{\tilde{\Pi}}\{|\sigma_{j_i}(\tilde{X}) - \hat{q}_{j_i}^N| \geq x\} \leq \mathbb{E}_{\tilde{\Pi}}[g(\tilde{X})]e^{-\theta x} \leq \eta_{10} e^{-\theta x}. \quad (16)$$

By the definition of $\tilde{X}_i^*$ (recall (3) and (5)), $\sum_{k \in A_p(j_i)} X_k^t + \sum_{k \in A_{lp}(j_i)} X_k^{*,t} = q_{j_i}^N$ so that

$$\tilde{X}_i^* = q_{j_i}^N - \sum_{k \in A_p(j_i)} \tilde{X}_k - \sum_{k \in A_{lp}(j_i) \setminus i} \tilde{X}_k^*$$

$$= q_{j_i}^N - \sigma_{j_i}(\tilde{X}) + \tilde{X}_i$$

$$= \hat{q}_{j_i}^N + \delta_i \log N - \sigma_{j_i}(\tilde{X}) + \tilde{X}_i,$$

and $|\tilde{X}_i - \tilde{X}_i^* + \delta_i \log N| = |\sigma_{j_i}(\tilde{X}) - \hat{q}_{j_i}^N|$. Using (16), we then have

$$\mathbb{P}_{\tilde{\Pi}}\{|\tilde{X}_i - \tilde{X}_i^* + \delta_i \log N| \geq x\}$$

$$= \mathbb{P}_{\tilde{\Pi}}\{|\sigma_{j_i}(\tilde{X}) - \hat{q}_{j_i}^N| \geq x\} \leq \eta_{11} e^{-\theta x}, \quad (17)$$

which implies

$$\mathbb{P}_{\tilde{\Pi}}\{|\tilde{X}_i - \tilde{X}_i^*| \geq \delta_i \log N + x\} \leq \eta_{12} e^{-\theta x}. \quad (18)$$

In turn,

$$\mathbb{E}_{\tilde{\Pi}}[|\tilde{X}_i - \tilde{X}_i^*|] \leq \eta_{13} \log N. \quad (19)$$

By Jensen's inequality, we also have that

$$\mathbb{E}_{\tilde{\Pi}}[f(\tilde{X})] = \mathbb{E}_{\tilde{\Pi}}[|\sigma_{j_i}(\tilde{X}) - \hat{q}_{j_i}^N|]$$

$$\leq \log \mathbb{E}_{\tilde{\Pi}}[g(\tilde{X})] \leq \log \eta_{14}.$$

By (9), $\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i^*] = y_i^* N$, and we conclude from (19) that

$$\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i] = y_i^* N + O(\log N).$$

Repeating this argument for all $i \in A_{lp}$, we have

$$\mathbb{E}_{\tilde{\Pi}}[\tilde{X}_i] = y_i^* N + O(\log N), i \in [n],$$

with $y_i^* = \lambda_i/\mu_i$ for $i \in A_p$, and we arrive at the first statement of the proposition, namely, that

$$r_\mu' \mathbb{E}_{\tilde{\Pi}}[\tilde{X}] = R(q^N, \lambda^N/\mu) + O(\log N).$$

We turn to the second statement of the proposition, namely, that $\tilde{\Pi}(\tilde{X} \setminus X^*) = O(N^{-m\delta_{\min}})$ for some $m > 0$. Recall that the true (not corrected) head count satisfies

$$\tilde{\Sigma}_{j_i} = \sigma_{j_i}(\tilde{X}) - \tilde{Z}_{j_i}' = \sigma_{j_i}(\tilde{X}) - \sum_{k \in A_{lp}(j_i)} (\tilde{X}_k^* - \tilde{X}_k)$$

$$= \sigma_{j_i}(\tilde{X}) - \sum_{k \in A_{lp}(j_i) \setminus i} \delta_k \log N$$

$$- \sum_{k \in A_{lp}(j_i) \setminus i} (\tilde{X}_k^* - \tilde{X}_k - \delta_k \log N),$$

and let $\tilde{\delta}_{j_i} = \sum_{k \in A_{lp}(j_i) \setminus i} \delta_k$. Then,

$$|\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| \leq \sum_{i \in A_{lp}} |\tilde{X}_i^* - \tilde{X}_i - \delta_i \log N|.$$

Using (17), a union bound gives

$$\mathbb{P}_{\tilde{\Pi}}\{|\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| \geq x\} \leq \eta_{15} e^{-\eta_{16} x}.$$

Note that $\tilde{X} \in \tilde{X} \setminus X^*$ if and only if there exists $i$ such that $\tilde{\Sigma}_{j_i} \geq \hat{q}_{j_i}^N + \delta_i \log N = q_{j_i}^N$. Then,

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{\Sigma}_{j_i} \geq \hat{q}_{j_i}^N + \delta_i \log N\}$$

$$\leq \mathbb{P}_{\tilde{\Pi}}\left\{|\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| > \frac{\delta_i}{2} \log N\right\}$$

$$+ \mathbb{P}_{\tilde{\Pi}}\left\{\tilde{\Sigma}_{j_i} \geq \hat{q}_{j_i}^N + \delta_i \log N, |\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| \leq \frac{\delta_i}{2} \log N\right\}.$$

On the event $\{|\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| \leq \frac{\delta_i}{2} \log N\}$, $\tilde{\Sigma}_{j_i} \geq \hat{q}_{j_i}^N + \delta_i \log N$ implies that $\sigma_{j_i}(\tilde{X}) \geq \hat{q}_{j_i}^N + \tilde{\delta}_{j_i} \log N + \frac{\delta_i}{2} \log N$. We then have, using (16), that

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{X} \in \tilde{X} \setminus X^*\}$$

$$\leq \sum_{i \in A_{lp}} \mathbb{P}_{\tilde{\Pi}}\{\tilde{\Sigma}_{j_i} \geq \hat{q}_{j_i}^N + \delta_i \log N\}$$

$$\leq \sum_{i \in A_{lp}} \mathbb{P}_{\tilde{\Pi}}\left\{|\tilde{\Sigma}_{j_i} - \sigma_{j_i}(\tilde{X}) + \tilde{\delta}_{j_i} \log N| > \frac{\delta_i}{2} \log N\right\}$$

$$+ \mathbb{P}_{\tilde{\Pi}}\left\{\sigma_{j_i}(\tilde{X}) \geq \hat{q}_{j_i}^N + \tilde{\delta}_{j_i} \log N + \frac{\delta_i}{2} \log N\right\}$$

$$\leq \eta_{17} N^{-\eta_{18} \delta_{\min}},$$
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                                           2111

with δ<sub>min</sub> = min<sub>i</sub>δ<sub>i</sub>, and we can take, in the statement of the proposition, m = η<sub>18</sub>. □

**Proposition 5.2 (Mixing Time Bound for X<sup>t</sup>).** The mixing time of X<sup>t</sup> satisfies

$$τ_{mix}(N^{-1}) = O(N^2 \log N).$$

## 5.4. Step 2: The Mixing Time Bound of X<sup>t</sup> Under CHT

In this second step, we return to the original (versus the auxiliary) Markov chain X<sup>t</sup>—on the state space 𝒳 and induced by CHT—and study its mixing time. Throughout this section, X<sup>t</sup> refers to this chain.

The mixing time of a continuous-time Markov chain M<sup>t</sup> on a finite state space ℳ and that has a steady-state distribution Π is defined as

$$τ_{mix}(ε) = \min\left\{t > 0 : \left|\frac{h_t(x,y) - Π(y)}{Π(y)}\right| ≤ ε \text{ for all } x,y ∈ ℳ\right\},$$

where h<sub>t</sub>(x,y) = ℙ<sub>x</sub>{M<sup>t</sup> = y} is the transition kernel, and it is given by the matrix exponential h<sub>t</sub> = e<sup>tQ</sup>, where Q is the transition rate matrix.

**Lemma 5.4 (Morris and Peres 2005, Theorem 13).** Let M<sup>t</sup> be a Markov chain on a finite state space ℳ with generator matrix Q = (Q<sub>xy</sub>)<sub>x,y∈ℳ</sub> and steady-state distribution Π. Define the uniformization constant U ≡ max<sub>x∈ℳ</sub> ∑<sub>y≠x</sub> Q<sub>xy</sub> and the (discrete-time) transition probability matrix P = U<sup>-1</sup>Q + I. Define also the conductance of P as

$$Φ = \min_{S⊂ℳ, Π(S) ≤ 1/2} \frac{∑_{x∈S,y∈S^c} Π(x)P_{xy}}{Π(S)}.$$

The mixing time of M<sup>t</sup> then satisfies

$$τ_{mix}(ε) ≤ U^{-1}\left[1 + \frac{8}{Φ^2}\log\frac{1}{εΠ_*}\right],$$                    (20)

where Π<sub>*</sub> = min<sub>x∈𝒳</sub>Π(x).

For the uniformization constant for X<sup>t</sup> (operated under CHT), we take

$$U = N\sum_{i∈[n]} λ_i + \max_{x∈𝒳} \sum_{i∈[n]} μ_i x_i.$$                    (21)

**Lemma 5.5.** Let Π be the stationary distribution of X<sup>t</sup>, let Q be its transition rate matrix and P = U<sup>-1</sup>Q + I with U in (21). There then exist constants c, K > 0 (not depending on N) such that

$$Π_* = \min_{x∈𝒳} Π(x) ≥ (cN)^{-KN},$$                    (22)

and

$$Φ = \min_{S⊂𝒳, Π(S) ≤ 1/2} \frac{∑_{x∈S,y∈S^c} Π(x)P_{xy}}{Π(S)} ≥ \frac{1}{cN}.$$                    (23)

Armed with this lemma, we bound the mixing time of the Markov chain X<sup>t</sup> in terms of the scaling factor N. The bound is given in the following proposition.

**Proof.** Under CHT, X<sup>t</sup> = x is subject to the resource constraint Ax ≤ q<sup>N</sup>. Because S(i) ≠ ∅, i ∈ [n], x<sub>i</sub> ≤ max<sub>j∈[d]</sub> q<sub>j</sub><sup>N</sup> so that the uniformization constant satisfies

$$U = N\sum_{i∈[n]} λ_i + \max_{x∈𝒳} \sum_{i∈[n]} μ_i x_i$$

$$≤ \sum_{i∈[n]} (λ_i + μ_i q_{max})N = O(N),$$

where q<sub>max</sub> = max<sub>j∈[d]</sub>q<sub>j</sub>. Taking ε = 1/N in Lemma 5.4 and plugging there also the lower bounds for Φ and Π<sup>*</sup> from Lemma 5.5, we obtain

$$τ_{mix}(N^{-1}) ≤ U^{-1}\left[1 + \frac{8}{(cN)^{-2}}\log\frac{1}{N^{-1}(cN)^{-KN}}\right]$$

$$= O(N^2 \log N),$$

as needed. □

## 5.5. Step 3: The Decoupling Time of X<sup>t</sup> and X̃<sup>t</sup>

The two chains X<sup>t</sup> and X̃<sup>t</sup> are easily constructed on the same sample space so that, having the same initial state at t = 0, X<sup>t</sup> = X̃<sup>t</sup> up to the time at which X̃<sup>t</sup> leaves 𝒳. We bound the tail of this exit time.

**Proposition 5.3 (Tail-Probability Bound).** The time that X̃<sup>t</sup> exits 𝒳

$$τ̃ := \inf\{t ≥ 0 : X̃^t ∈ X̃\backslash𝒳\}$$

satisfies

$$ℙ_Π\{τ̃ ≤ t\} ≤ (1 + m_1 Nt)(Π̃(X̃\backslash𝒳) + \exp(-m_2 N)),$$

for some constants m<sub>1</sub>, m<sub>2</sub> > 0 that do not depend on N.

**Proof.** We note that τ̃ ≤ t if and only if the number of visits of (entries to) X̃<sup>t</sup> the set ℰ := X̃\backslash𝒳 by time t is greater than or equal to one. This can happen only if either (i) the chain is in ℰ at time t = 0 or (ii) the chain enters a state x ∈ 𝒳 with (Ax)<sub>j</sub> = q<sub>j</sub><sup>N</sup> for some j ∈ [d] and exits it into ℰ by time t; in particular, the chain visits some state in ℰ at least once.

For any x ∈ X̃, let N<sup>t</sup>(x) be the number of exits of X̃<sup>t</sup> from the state by time t. For a subset C ⊆ X̃, let N<sup>t</sup>(C) = ∑<sub>x∈C</sub>N<sup>t</sup>(x). Then,

$$ℙ_Π̃\{τ̃ ≤ t\} ≤ Π̃(ℰ) + ℙ_Π̃\{N^t(Ẽ) ≥ 1\}$$

$$≤ Π̃(ℰ) + 𝔼_Π̃[N^t(Ẽ)] = Π̃(ℰ) + \sum_{x∈Ẽ} 𝔼_Π̃[N^t(x)],$$

where the second inequality is Markov's. It is a basic fact of continuous-time Markov chains that 𝔼_Π̃[N<sup>t</sup>(x)] = e(x)Π̃(x)t, where e(x) is the total exit rate from state x.
---


2112                                    Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
                                       Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

Recalling that $\tilde{X} = \{x \in \mathbb{Z}_+^n : x_i \leq q_{ji}^N - \delta_i \log N$ for all $i \in A_{lp}\}$, we have

$$e(x) \leq \sum_{i \in [n]} \lambda_i^N + \sum_{i \in A_p} \mu_i x_i + \sum_{i \in A_{lp}} \mu_i q_{ji}^N$$

$$\leq \eta_1 N + \mu \sum_{i \in A_p} x_i, x \in \tilde{X},$$

where $\mu := \max_i \mu_i$. Defining

$$B := \left\{x \in \tilde{X} : \sum_{i \in A_p} x_i \leq 2\sum_{i \in A_p} \lambda_i^N/\mu_i\right\},$$

we have

$$e(x) \leq \eta_1 N + 2\mu \sum_{i \in A_p} \lambda_i^N/\mu_i \leq \eta_2 N, x \in \mathcal{E} \cap B.$$

Then,

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\}$$

$$\leq \tilde{\Pi}(\mathcal{E}) + \sum_{x \in \mathcal{E}} \mathbb{E}_{\tilde{\Pi}}[N^t(x)]$$

$$= \tilde{\Pi}(\mathcal{E}) + \sum_{x \in \mathcal{E} \cap B} e(x)\tilde{\Pi}(x)t + \sum_{x \in \mathcal{E} \backslash B} e(x)\tilde{\Pi}(x)t$$

$$\leq \tilde{\Pi}(\mathcal{E}) + \eta_3 N\tilde{\Pi}(\mathcal{E})t + \eta_4 t\left(N + \sum_{x \in \mathcal{E} \backslash B} \|x_{A_p}\|\tilde{\Pi}(x)\right)$$

$$= \tilde{\Pi}(\mathcal{E}) + \eta_3 N\tilde{\Pi}(\mathcal{E})t + \eta_4 t\mathbb{E}_{\tilde{\Pi}}[(N + \|\tilde{X}_{A_p}\|)1\{\|\tilde{X}_{A_p}\|$$

$$> 2\|(\lambda^N/\mu)_{A_p}\|\}],$$
(24)

where $\|\cdot\|$ is the $L_1$ norm. We turn to bound the second term in the last row. Under $\tilde{\Pi}$, recall that $(\tilde{X}_i, i \in A_p)$ are independent Poisson random variables with mean $\lambda_i^N/\mu_i$ for $i \in A_p$. In turn, $\|\tilde{X}_{A_p}\| \sim \text{Poisson}((\lambda^N/\mu)_{A_p})$. By Hölder's inequality,

$$\mathbb{E}_{\tilde{\Pi}}[(N + \|\tilde{X}_{A_p}\|)1\{\|\tilde{X}_{A_p}\| > 2\|(\lambda^N/\mu)_{A_p}\|\}]$$

$$\leq \sqrt{\mathbb{E}_{\tilde{\Pi}}[(N + \|\tilde{X}_{A_p}\|)^2]}\sqrt{\mathbb{P}_{\tilde{\Pi}}\{\|\tilde{X}_{A_p}\| > 2\|(\lambda^N/\mu)_{A_p}\|\}}$$

$$\leq \eta_4 Ne^{-\eta_5(\lambda^N/\mu)_{A_p}} \leq \eta_6 Ne^{-\eta_7 N},$$
(25)

where we used the second moment of the Poisson random variable $\mathbb{E}[\|\tilde{X}_{A_p}\|^2] = \|(\lambda^N/\mu)_{A_p}\| + \|(\lambda^N/\mu)_{A_p}\|^2$, and standard tail bounds for the Poisson distribution. Plugging (25) into (24), we conclude that

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\} \leq (1 + \eta_8 Nt)(\tilde{\Pi}(\mathcal{E}) + e^{-\eta_9 N}),$$

as required. □

## 5.6. Combining the Steps

The process $X^t$ has the state space

$$\mathcal{X} = \{x \in \mathbb{Z}_+^n : Ax \leq q^N\}$$

$$\cap \{x \in \mathbb{Z}_+^n : x_i \leq q_{ji}^N - \delta_i \log N + 1 \text{ for all } i \in A_{lp}\}$$

$$\subset \tilde{X} = \{x \in \mathbb{Z}_+^n : x_i \leq q_{ji}^N - \delta_i \log N + 1 \text{ for all } i \in A_{lp}\}.$$

It cannot be initialized with the stationary distribution of $\tilde{X}$ as the latter may assign positive probabilities to states outside of $\mathcal{X}$. To that end, fix $x_0 \in \mathcal{X}$ and define the distribution $\Pi^0$ on $\mathcal{X}$ by

$$\Pi^0(x) = \begin{cases}
\tilde{\Pi}(x) & \text{if } x \neq x_0, \\
\tilde{\Pi}(x_0) + \tilde{\Pi}(\tilde{X} \backslash \mathcal{X}) & \text{if } x = x_0.
\end{cases}$$

Then, $\mathbb{E}_{\Pi^0}[X^t] = \sum_{x \in \mathcal{X}} \tilde{\Pi}(x)\mathbb{E}_x[X^t] + \tilde{\Pi}(\tilde{X} \backslash \mathcal{X})\mathbb{E}_{x_0}[X^t]$. We define $\mathbb{E}_{\tilde{\Pi}}[X^t] := \mathbb{E}_{\Pi^0}[X^t]$.

For any $t > 0$,

$$|\mathbb{E}_{\tilde{\Pi}}[r_\mu' \tilde{X}^t] - \mathbb{E}_{\tilde{\Pi}}[r_\mu' X^t]| \leq |\mathbb{E}_{\tilde{\Pi}}[r_\mu' \tilde{X}^t] - \mathbb{E}_{\tilde{\Pi}}[r_\mu X^t]|$$

$$+ |\mathbb{E}_{\tilde{\Pi}}[r_\mu' X^t] - \mathbb{E}_{\tilde{\Pi}}[r_\mu' X^t]|.$$
(26)

Recall

$$\tilde{\tau} := \inf\{t \geq 0 : \tilde{X}^t \in \tilde{X} \backslash \mathcal{X}\}.$$

Because $X^t$ and $\tilde{X}^t$ have the same transition rates inside $\mathcal{X}$, it is straightforward to build them on the same probability space so that $X^t = \tilde{X}^t$ up to $\tilde{\tau}$. We can write, for any $t \geq 0$,

$$\mathbb{E}_{\tilde{\Pi}}[r_\mu' \tilde{X}^t] - \mathbb{E}_{\tilde{\Pi}}[r_\mu' X^t] = \mathbb{E}_{\tilde{\Pi}}[r_\mu'(\tilde{X}^t - X^t) 1\{\tilde{\tau} \leq t\}].$$

Because $r_\mu' x \leq \eta_1 N$ for any state $x \in \mathcal{X}$ (take $\eta_1 = (\max_i r_{i\mu_i})q_{\max}$), we have $\mathbb{E}_{\tilde{\Pi}}[(r_\mu' X^t)^2] \leq \eta_2 N$. Because $\tilde{X}_i^t \leq q_{\max} N$ for all $i \in A_{lp}$ and, in steady-state, $\tilde{X}_i^t \sim \text{Poisson}(\lambda_i^N/\mu_i)$ for all $i \in A_p$, we have that $\mathbb{E}_{\tilde{\Pi}}[(r_\mu' \tilde{X}^t)^2] \leq \eta_3 N^2$.

Using Hölder's inequality, we then have

$$|\mathbb{E}_{\tilde{\Pi}}[r_\mu'(\tilde{X}^t - X^t)1\{\tilde{\tau} \leq t\}]|$$

$$\leq |\mathbb{E}_{\tilde{\Pi}}[r_\mu' \tilde{X}^t 1\{\tilde{\tau} \leq t\}]| + |\mathbb{E}_{\tilde{\Pi}}[r_\mu' X^t 1\{\tilde{\tau} \leq t\}]|$$

$$\leq \sqrt{\mathbb{E}_{\tilde{\Pi}}[(r_\mu' \tilde{X}^t)^2]}\sqrt{\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\}}$$

$$+ \sqrt{\mathbb{E}_{\tilde{\Pi}}[(r_\mu' X^t)^2]}\sqrt{\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\}}$$

$$\leq \eta_4 N\sqrt{\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\}}.$$

Setting $t = \tau(N^{-1}) = O(N^2 \log N)$, we have, by Propositions 5.2 and 5.3, that

$$\mathbb{P}_{\tilde{\Pi}}\{\tilde{\tau} \leq t\} \leq (1 + \eta_5 Nt)(\tilde{\Pi}(\tilde{X} \backslash \mathcal{X}) + \exp(-\eta_6 N))$$

$$\leq \eta_7 N^3 \log N(\tilde{\Pi}(\tilde{X} \backslash \mathcal{X}) + \exp(-\eta_6 N)),$$

and by Proposition 5.1, $\tilde{\Pi}(\tilde{X} \backslash \mathcal{X}) = O(N^{-m\delta_{\min}})$. We can
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                                                                           2113

choose the threshold coefficients δ<sub>i</sub>, i ∈ A<sub>p</sub> large enough so that mδ<sub>min</sub> ≥ 8, in which case

$$|E_{\tilde{\Pi}}[r'_{\mu}(\tilde{X}^t - X^t) \mathbf{1}\{\tilde{\tau} \leq t\}]|$$

$$\leq \eta_4 N \sqrt{P_{\tilde{\Pi}}\{\tilde{\tau} \leq t\}} \leq \eta_8(N^3 \log N)N^{-4} \leq \eta_9.$$

We conclude for the first summand on the right of (26) that $|E_{\tilde{\Pi}}[r'_{\mu}\tilde{X}^t] - E_{\Pi}[r_{\mu}X^t]| = O(1)$.

For the second summand, we have, by definition, that $|h_t(x, y) - \Pi(y)| \leq \frac{1}{N}\Pi(y)$ at t = τ(N<sup>-1</sup>) and for all x ∈ X. Because r'<sub>μ</sub>x ≤ η<sub>1</sub>N for any x ∈ X, we have, for this t, that

$$|r'_{\mu}E_x[X^t] - r'_{\mu}E_{\Pi}[X]| \leq \frac{1}{N}r'_{\mu}E_{\Pi}[X] \leq \eta_{10}.$$

In turn,

$$|E_{\tilde{\Pi}}[r'_{\mu}X^t] - E_{\Pi}[r_{\mu}X^t]|$$

$$= \left|\sum_{x∈X} \Pi(x)(E_x[r'_{\mu}X^t] - E_{\Pi}[r'_{\mu}X^t])\right|$$

$$\leq \max_{x∈X} |r'_{\mu}E_x[X^t] - r'_{\mu}E_{\Pi}[X]| \leq \eta_{10} = O(1),$$

which bounds the second summand in (26) and concludes the proof. □

## 6. Proof of the Lower Bound in Theorem 4.1

We note that, because (i) the state space is finite and the action sets—corresponding to admission probabilities—compact and (ii) the MDP is unichain<sup>3</sup> we can assume, without loss of generality, that—for each N—there exists a stationary deterministic optimal policy (see theorem 11.4.6 of Puterman 2014). We let π<sup>*,N</sup> be the optimal policy in the Nth system, Π<sup>*,N</sup> be the steady-state distribution under this policy, and R<sup>*,N</sup> be the optimal long-run average reward.

In Lemma 6.1, we establish some properties that must be satisfied for sublogarithmic regret. Specifically, for the optimal policy to have o(log N) regret, it must (i) keep all, up to o(log N), resource units busy in stationarity, and (ii) it must accept all, up to o(log N), customers of types i ∈ A<sub>p</sub>.

In step 2, we use Markov chain analysis to show that a policy that satisfies properties (i) and (ii) must have a stationary distribution that assigns a nonnegligible probability to states in which all servers are busy. In those states all, in particular, preferred customers are rejected. Because that probability is nonnegligible, we have more Ω(log N) customers rejected of types i ∈ A<sub>p</sub>, contradicting the o(log N) gap.

**Lemma 6.1.** Suppose that

$$R(q^N, λ^N/μ) - R^{*,N} = o(\log N).$$                                                                          (27)

Then, x<sub>i</sub><sup>N</sup> = E<sub>Π<sup>*,N</sup></sub>[X<sub>i</sub>] must satisfy

$$Ax^N = q^N - o(\log N),$$                 (28)

and

$$x_i^N = \frac{λ_i^N}{μ_i} - o(\log N), i ∈ A_p.$$                (29)

Recall that Σ<sub>j</sub> = (AX)<sub>j</sub> is the head count in station j. Markov's inequality applied to (28), gives that, for all j ∈ [d] and any κ > 0,

$$P_{\Pi^{*,N}}\{q_j^N - Σ_j > κ \log N\} \leq \frac{o(\log N)}{κ \log N} → 0, \text{ as } N ↑ ∞.$$
                                                                     (30)

We prove (30) implies that (29) is violated and leads, in turn, to a contradiction to (27).

For the rest of this proof, we fix a resource j ∈ [d] whose constraint in (LP) is binding and for which there exists a request i ∈ A<sub>p</sub> with A<sub>ji</sub> = 1; such j and i exist by assumption. We denote this type by i<sub>0</sub> and drop the subscript j from all subsequent notation in this proof. Let s(x) = Σ<sub>i</sub>A<sub>ji</sub>x<sub>i</sub> be the total resource j head count in state x ∈ X; Σ = s(X) is then the stationary resource head count under π<sup>*,N</sup>. Fix κ > 0, let K<sub>N</sub> = ⌈κ log N⌉, and for l = 0, 1, ..., K<sub>N</sub> consider the sets

B(l) := {x ∈ X : q<sup>N</sup> - s(x) ≤ K<sub>N</sub> - l}
= {x ∈ X : s(x) ≥ q<sup>N</sup> - K<sub>N</sub> + l},

and define B(K<sub>N</sub> + 1) := ∅. Then, B(K<sub>N</sub>) ⊆ B(l) ⊆ B(0) for all l ∈ [K<sub>N</sub>]. For l = 0, 1, ..., K<sub>N</sub> define also

B<sub>-</sub>(l) := B(l)\B(l + 1) = {x ∈ X : s(x) = q<sup>N</sup> - K<sub>N</sub> + l}.

Notice that B(0) = ∪<sub>l=0</sub><sup>K<sub>N</sub></sup>B<sub>-</sub>(l).

Let λ<sub>Σ</sub> = Σ<sub>i</sub>λ<sub>i</sub>, μ̄ = max<sub>i</sub>μ<sub>i</sub>, and μ = min<sub>i</sub>μ<sub>i</sub>. Transitions in the Markov chain X<sup>t</sup> induced by the optimal policy π<sup>*,N</sup> are either of the form x → x + e<sub>k</sub> (accepting an arriving type k request) or x → x - e<sub>k</sub> (a type k service completion). For any set C ⊆ X and its complement C<sup>c</sup> = X\C,

$$\sum_{x∈C, z∈C^c} \Pi(x)Q(x, z) = \sum_{z∈C^c, x∈C} \Pi(z)Q(z, x);$$

see, for example, exercise 5.34 in Ross (1996). Take these sets to be C = B(l) and C<sup>c</sup> = B(l)<sup>c</sup>. For x ∈ B(l), z ∈ B(l)<sup>c</sup>, s(x) ≥ q<sup>N</sup> - K<sub>N</sub> + l and s(z) < q<sup>N</sup> - K<sub>N</sub> + l. If Q(x, z) > 0, there must exist k ∈ [n] such that z = x - e<sub>k</sub> and x ∈ B<sub>-</sub>(l), z ∈ B<sub>-</sub>(l - 1). Similarly, for z ∈ B(l)<sup>c</sup>, x ∈ B(l) with Q(z, x) > 0, there must exist k ∈ [n] such that x = z + e<sub>k</sub> and z ∈ B<sub>-</sub>(l - 1), x ∈ B<sub>-</sub>(l).

Recall that i<sub>0</sub> ∈ A<sub>p</sub> is such that A<sub>ji<sub>0</sub></sub> = 1. By (29), x<sub>i<sub>0</sub></sub> = E<sub>Π<sup>*,N</sup></sub>[X<sub>i<sub>0</sub></sub>] = λ<sub>i<sub>0</sub></sub><sup>N</sup>/μ<sub>i<sub>0</sub></sub> - o(log N), implying that

$$\sum_{x∈X:Q(x, x+e_{i_0})=0} \Pi(x) = o(\log N/N).$$
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2114                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

Otherwise, if $$\sum_{x \in X: Q(x,x+e_{i_0}) \neq 0} \Pi(x) = \Omega(\log N/N)$$, PASTA implies that $$\Omega(\lambda_{i_0}^N \log N/N) = \Omega(\log N)$$ type $$i_0$$ customers arrive in those states $$x$$ and are rejected; alternatively, at most $$\lambda_{i_0}^N - \Omega(\log N)$$ are accepted. By Little's law, we would then have $$E_{\Pi^{*,N}}[X_i] = \frac{1}{\mu_i}(\lambda_{i_0}^N - \Omega(\log N)) = \frac{\lambda_{i_0}^N}{\mu_i} - \Omega(\log N)$$.

In turn,
$$\sum_{z \in B(l)^c, x \in B(l)} \Pi(z)Q(z, x)$$

$$\geq \sum_{z \in B^c(l-1), x=z+e_{i_0}} \Pi(z)Q(z, x)$$

$$= \lambda_{i_0}^N \left( \sum_{z \in B^c(l-1), Q(z,z+e_{i_0})>0} \Pi(z) \right)$$

$$= \lambda_{i_0}^N \left( \sum_{z \in B^c(l-1)} \Pi(z) - \sum_{z \in B^c(l-1), Q(z,z+e_{i_0})=0} \Pi(z) \right)$$

$$= \lambda_{i_0}^N (\Pi(B^c(l - 1)) - o(\log N/N)).$$                                           (31)

Recalling that transition from $$B(l)$$ to $$B(l)^c$$ must be of the form $$z = x - e_k$$, we have
$$\sum_{x \in B(l), z \in B(l)^c} \Pi(x)Q(x, z) \leq \Pi(B^c(l))\mu qN.$$

Then,
$$\lambda_{i_0}^N (\Pi(B^c(l - 1)) - o(\log N/N))$$
$$\leq \sum_{z \in B(l)^c, x \in B(l)} \Pi(z)Q(z, x) = \sum_{x \in B(l), z \in B(l)^c} \Pi(x)Q(x, z)$$
$$\leq \Pi(B^c(l))\mu qN,$$

where the first inequality follows from (31). Recalling that $$\lambda_{i_0}^N = N\lambda_{i_0}$$, we have
$$\lambda_{i_0} (\Pi(B^c(l - 1)) - o(\log N/N)) \leq \Pi(B^c(l))\mu q.$$                                  (32)

Applying this recursively, we have the lower bound
$$\Pi(B^c(l)) \geq \delta^l \Pi(B^c(0)) - \frac{1}{1 - \delta} \times o(\log N/N)$$
$$= \Pi(B^c(0)) - o(\log N/N),$$                            (33)

where $$\delta := \lambda_{i_0}/\mu q < 1$$.

We next obtain a lower bound on $$\Pi(B^c(0))$$. Because transitions from $$B(l)^c$$ to $$B(l)$$ must be of the form $$x = z + e_k$$, the rate of these transitions is bounded by $$\sum \lambda_i^N = N\sum \lambda_i = N\lambda_{\Sigma}$$; that is, $$\lambda_{\Sigma}N\Pi(B^c(l)) \geq \sum_{z \in B(l)^c,x \in B(l)}\Pi(z)Q(z, x)$$.

For all $$l = \{0, 1, \ldots, K_N\}$$, we have that $$|q^N - s(x)| \leq K_N$$ and, in particular, that $$s(x) \geq q^N/2 = qN/2$$ for all sufficiently large $$N$$. Thus, $$Q(x, z) \geq \mu qN/2$$, and we have
$$\lambda_{\Sigma}N\Pi(B^c(l - 1)) \geq \sum_{z \in B(l)^c, x \in B(l)} \Pi(z)Q(z, x)$$
$$= \sum_{x \in B(l), z \in B(l)^c} \Pi(x)Q(x, z) \geq \Pi(B^c(l))\mu qN/2.$$                   (34)

We then have the upper bound
$$\Pi(B^c(l)) \leq \tilde{\delta}^l \Pi(B^c(0)),$$             (35)

where $$\tilde{\delta} := \lambda_{\Sigma}/\mu q/2 > 1 > \lambda_{i_0}/\mu q = \delta$$. Here, we recall that the resource constraint for $$j$$ is binding so that $$\lambda_{\Sigma}/\mu \geq \sum \lambda_i/\mu_i \geq \sum A_{ji}\psi_i^* = q$$.

The set $$\{l \in \{0, \ldots, K_N - 1\} : \sum_{x \in B(l),z \in B(l+1)}Q(x, z) > 0\}$$ is nonempty. Indeed, if it were empty (meaning that no requests are accepted when there are $$q^N - K_N$$ or more units of server $$j$$ busy), then we would have that $$(Ax^N)_j \leq q^N - K_N$$, which because $$K_N = \Omega(\log N)$$, would contradict (28). Let

$$l^* := \max\left\{l \in \{0, \ldots, K_N - 1\} : \sum_{x \in B(l), z \in B(l+1)} Q(x, z) > 0\right\}.$$

Recall that $$B(0) = \{x \in X : s(x) \geq q^N - K_N\} = \bigcup_{l=0}^{K_N-1} B^c(l)$$. By (30), $$\Pi(B(0)) = P_{\Pi^{*,N}}\{s(X) \geq q^N - K_N\} \geq \frac{1}{2}$$ for all large enough $$N$$. Using the upper bound (35), and recalling that $$\tilde{\delta} > 1$$, we have that

$$\frac{1}{2} \leq \Pi(B(0)) = \sum_{l=0}^{K_N} \Pi(B^c(l)) \leq K_N \Pi(B^c(0))\tilde{\delta}^{K_N}.$$

In turn,
$$\Pi(B^c(0)) \geq \frac{2}{K_N \tilde{\delta}^{K_N}}.$$

Using now the lower bound (33), and recalling that $$K_N = \lceil \kappa\log N \rceil$$ for some $$\kappa > 0$$, we have

$$\Pi(B^c(l)) \geq \frac{2}{K_N} \left(\frac{\delta}{\tilde{\delta}}\right)^{K_N} - o(\log N/N)$$

$$\geq \frac{2}{\kappa \log N + 1} \left(\frac{\delta}{\tilde{\delta}}\right)^{\kappa \log N+1} - o(\log N/N).$$

Letting $$\beta = -\log(\delta/\tilde{\delta})$$, we then have, for all $$l \in [l^* + 1]$$,

$$\Pi(B^c(l)) \geq 2e^{-\beta} \frac{1}{N^{\beta\kappa}(\kappa \log N + 1)} - o(\log N/N)$$

$$\geq 2e^{-\beta} \frac{1}{N^{\kappa(\beta+1)}} - o(\log N/N),$$

where, in the last inequality, we use the fact that, for fixed $$\kappa$$ and all $$N$$ large, $$N^{\kappa} \geq \kappa\log N + 1$$. Choosing $$\kappa = \frac{2}{\beta+1}$$, we then have, for all such $$l$$ (including $$l^* + 1$$), that

$$\Pi(B^c(l)) \geq 2e^{-\beta} \frac{1}{\sqrt{N}} - o(\log N/N).$$

By definition, no arrivals of type $$i_0 \in A_p$$ are accepted in states $$x \in B^c(l^* + 1)$$. In turn, $$\{x \in X : Q(x, x + e_{i_0}) = 0\} \supseteq B^c(l^* + 1)$$. Using PASTA, we then have that the number
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                                        2115

of type i<sub>0</sub> customers rejected per unit of time is

$$\lambda^N_{i_0} \sum_{x:Q(x,x+e_{i_0})\neq 0} \Pi(x) \geq \lambda^N_{i_0} \Pi(B_-(l^* + 1))$$

$$\geq \lambda^N_{i_0} \left(2e^{-\beta} \frac{1}{\sqrt{N}} - o(\log N/N)\right) = \Omega(\sqrt{N}).$$

The long-run number of type i<sub>0</sub> customers in system, x<sup>N</sup><sub>i</sub> = E<sub>Π<sup>*,N</sup></sub>[X<sub>i</sub>], then satisfies

$$x^N_i = \frac{\lambda^N_{i_0}}{\mu_{i_0}} - \Omega(\sqrt{N}),$$

contradicting (29) and, in turn, the optimality of the policy. □

**Remark 6.1** (Resources with Strictly Positive Slack Capacity). We recall that Assumption 3.1 allows for resources that have strictly positive slack in the optimal solution. Thus far, we have made the simplifying assumption that there are no such resources. Upon conclusion of the proofs, we are now in the position to explain why this restriction comes at no loss of generality. First, we note that these resources would not appear in the definition of the relaxed network or the relaxed policy and, hence, play no role in its analysis. These, it is evident, also will not change the mixing time result for the original network operated under CHT. In turn, these resources have no impact on the proof of the upper bound. For the lower bound, the assumption A<sub>p</sub> ≠ ∅ in Theorem 4.1 has to be strengthened to the requirement that A<sub>p</sub>(j) ≠ ∅ for at least one j whose capacity constraint is binding in the optimal solution to (LP).

## 7. Simulations

In this section, we present simulation results for the networks in Figures 2–4. In the simulations, we scale both the customer arrival rates λ<sup>N</sup><sub>i</sub> = Nλ<sub>i</sub> for all i ∈ [n] and the resource units q<sup>N</sup><sub>j</sub> = Nq<sub>j</sub> for all j ∈ [d] with N ∈ {100, 200, ..., 1, 500}. For each value of N, we run one long sample path and report the long-run average after dropping a warm-up period. Taking into account that mixing time increases with the network scale, we use a horizon length that scales up with N<sup>2</sup>.

**Network 1.** This is the network in Example 3.1, and we use the same parameters. The less-preferred customer types A<sub>lp</sub> = {2, 3} are those marked in color in Figure 2. The parameters for this particular setting are given by

λ = (3, 2, 5), μ<sup>-1</sup> = (2, 1, 3), q = (7, 6), r = (5, 1, 2).

We choose the coefficients of CHT as δ<sub>a</sub> = δ<sub>b</sub> = 20.

**Network 2.** This is the network in Example 3.2, and we use the same parameters. The less-preferred customer types A<sub>lp</sub> = {1, 2, 3, 5} are those marked in color in Figure 3. The parameters for this particular setting are given by

λ = (2, 3, 5, 1, 6, 2, 3), μ<sup>-1</sup> = (1, 3, 2, 3, 5, 4, 2),
q = (11, 19, 14, 7), r = (2, 1, 3, 5, 1, 6, 5).

We choose the coefficients of CHT as δ<sub>1</sub> = δ<sub>2</sub> = δ<sub>3</sub> = δ<sub>5</sub> = 20.

Figure 8 depicts the log-normalized regret of CHT as a function of the scaling factor N for networks 1 and 2. The plot suggests that the log-normalized regret does not grow with the scaling factor N, which echoes our results. For each of the networks, we also plot the log-normalized regret under a threshold policy with the same d logarithmic thresholds but in which these are

**Figure 8.** (Color online) Log-Normalized Regret for the Networks in Example 3.1 (Left) and Example 3.2 (Right)

<table>
<thead>
<tr>
<th>Network</th>
<th>Scaling Factor N</th>
<th>Local Threshold Policy</th>
<th>CHT</th>
</tr>
</thead>
<tbody>
<tr>
<td rowspan="8">Network 1<br>δ=(20, 20)</td>
<td>100</td>
<td>18</td>
<td>17</td>
</tr>
<tr>
<td>300</td>
<td>19</td>
<td>18</td>
</tr>
<tr>
<td>500</td>
<td>20</td>
<td>19</td>
</tr>
<tr>
<td>700</td>
<td>19</td>
<td>19</td>
</tr>
<tr>
<td>900</td>
<td>20</td>
<td>19</td>
</tr>
<tr>
<td>1100</td>
<td>18</td>
<td>20</td>
</tr>
<tr>
<td>1300</td>
<td>21</td>
<td>21</td>
</tr>
<tr>
<td>1500</td>
<td>19</td>
<td>19</td>
</tr>
<tr>
<td rowspan="8">Network 2<br>δ=(20, 20, 20, 20)</td>
<td>100</td>
<td>40</td>
<td>39</td>
</tr>
<tr>
<td>300</td>
<td>41</td>
<td>38</td>
</tr>
<tr>
<td>500</td>
<td>40</td>
<td>36</td>
</tr>
<tr>
<td>700</td>
<td>48</td>
<td>42</td>
</tr>
<tr>
<td>900</td>
<td>42</td>
<td>38</td>
</tr>
<tr>
<td>1100</td>
<td>41</td>
<td>37</td>
</tr>
<tr>
<td>1300</td>
<td>43</td>
<td>42</td>
</tr>
<tr>
<td>1500</td>
<td>39</td>
<td>37</td>
</tr>
</tbody>
</table>

Notes. The plot displays the (simulated) regret of CHT normalized by the natural log of the scaling factor N in the setting of Examples 3.1 and 3.2, respectively. Specifically, we simulate the regret—the difference between the LP upper bound and the long-run average reward—in a long time horizon with the warm-up period being dropped off. In both networks, the local threshold policy is the one that uses the true instead of the corrected head count.
---


2116                                                                Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
                                                                     Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

**Figure 9.** (Color online) Log-Normalized Regret for the Network in Figure 4 (Left)

<table>
<thead>
<tr>
<th>Scaling Factor, N</th>
<th>Corrected threshold policy δ=(10,10,10)</th>
<th>Local threshold policy δ=(20,20,20)</th>
</tr>
</thead>
<tbody>
<tr><td>100</td><td>28</td><td>28</td></tr>
<tr><td>200</td><td>28</td><td>28</td></tr>
<tr><td>300</td><td>29</td><td>29</td></tr>
<tr><td>400</td><td>29</td><td>29</td></tr>
<tr><td>500</td><td>29</td><td>29</td></tr>
<tr><td>600</td><td>29</td><td>29</td></tr>
<tr><td>700</td><td>28</td><td>28</td></tr>
<tr><td>800</td><td>28</td><td>28</td></tr>
<tr><td>900</td><td>28</td><td>28</td></tr>
<tr><td>1000</td><td>28</td><td>28</td></tr>
</tbody>
</table>

applied to the true head count Σ<sup>t</sup> instead of the corrected one Σ<sup>*,t</sup>; in the network of Figure 3, for instance, type 3 is accepted whenever feasible and q<sub>3</sub><sup>N</sup> − Σ<sup>t</sup> ≥ δ<sub>3</sub> log N. This natural threshold policy—with the threshold identified, still, based on the perfect matching—seems to perform as well as CHT. These two networks, however, have a special property: their LP-residual network graph has a unique perfect matching.

**Network 3.** We observe similar performance in the network of Example 3.3. Here, the parameters are

λ = (2, 2, 2, 1/2, 1/3, 1/4),    μ<sup>−1</sup> = (1, 1, 1, 2, 3, 4),
q = (2, 2, 2),    r = (1, 1, 1, 4, 6, 8).

We use the perfect matching (1, a), (2, b), (3, c) for the threshold assignment, accepting a type 1, for example, only if the head count of resource a is smaller than q<sub>a</sub><sup>N</sup> − δ<sub>1</sub> log N. In this network, the two policies—CHT and the regular (using the true head count) threshold policy—no longer show identical performance. They both have logarithmic regret, but the exact constant is different if the same thresholds are used. However, with these specific parameters, the same performance

that a threshold δ achieves in the regular threshold policy is achieved by δ/2 under CHT. This is shown in Figure 9, which depicts the log-normalized regret under the two policies. We tried different parameter combinations for this network, and all showed similarly stable and good performance.

Finally, we conducted a sensitivity analysis for the setting of network 1. In Table 1, we report CHT's stationary rewards for N = 1,000 and different values of the two thresholds (coefficients) δ<sub>a</sub>, δ<sub>b</sub>. The important threshold seems to be δ<sub>a</sub>, which protects the preferred type 1: the reward is highest when this threshold is large and is less sensitive to how big the threshold is for resource b. It is an interesting question, left for future work, to understand the dependence of the required threshold magnitude on the location of a resource in the network.

In Appendix B, we provide a simple heuristic to guide the choice of the threshold coefficients, accompanied by numerical evidence.

## 8. Closing Remarks

In this paper, we study a dynamic resource allocation problem with multiple types of customers and multiple types of reusable resource units. Under an overload condition on the associated LP of the problem, we devise a threshold policy—with the number of thresholds equal to the number of resources—and show that its regret is at most logarithmic in the problem size in the high-volume, many-server regime. The thresholds are applied to a corrected head count process at each resource.

The study of networks with simultaneous resource possession (as the ones we study here) inevitably leads to questions about the relationship between performance and the underlying combinatorial/graph structure; see also Gurvich and Van Mieghem (2015) and Dawande et al. (2021).

Our solution in this paper is based strongly on the existence, in overloaded networks, of a perfect matching in the residual graph. The policy we propose is a

**Table 1.** Performance of CHT with Different Coefficients

<table>
<thead>
<tr>
<th>δ<sub>a</sub> (R<sub>a</sub> = δ<sub>a</sub>logN)</th>
<th colspan="8">δ<sub>b</sub> (R<sub>b</sub> = δ<sub>b</sub> log N)</th>
</tr>
<tr>
<th></th>
<th>0.5</th>
<th>1</th>
<th>2</th>
<th>3</th>
<th>5</th>
<th>10</th>
<th>15</th>
<th>20</th>
</tr>
</thead>
<tbody>
<tr><td>0.5</td><td>18,727</td><td>18,672</td><td>18,659</td><td>18,655</td><td>18,646</td><td>18,623</td><td>18,600</td><td>18,577</td></tr>
<tr><td>1</td><td>19,076</td><td>19,055</td><td>19,049</td><td>19,043</td><td>19,033</td><td>19,010</td><td>18,987</td><td>18,964</td></tr>
<tr><td>2</td><td>19,299</td><td>19,293</td><td>19,289</td><td>19,285</td><td>19,276</td><td>19,253</td><td>19,230</td><td>19,207</td></tr>
<tr><td>3</td><td>19,327</td><td>19,326</td><td>19,322</td><td>19,318</td><td>19,308</td><td>19,286</td><td>19,262</td><td>19,239</td></tr>
<tr><td>5</td><td>19,328</td><td>19,328</td><td>19,323</td><td>19,318</td><td>19,309</td><td>19,285</td><td>19,263</td><td>19,239</td></tr>
<tr><td>10</td><td>19,319</td><td>19,315</td><td>19,310</td><td>19,305</td><td>19,296</td><td>19,272</td><td>19,250</td><td>19,227</td></tr>
<tr><td>15</td><td>19,306</td><td>19,303</td><td>19,299</td><td>19,293</td><td>19,284</td><td>19,260</td><td>19,237</td><td>19,213</td></tr>
<tr><td>20</td><td>19,296</td><td>19,294</td><td>19,287</td><td>19,282</td><td>19,273</td><td>19,250</td><td>19,228</td><td>19,204</td></tr>
</tbody>
</table>

*Note.* Example 1: The numbers are rounded to the nearest integer.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                      2117

centralized policy in which, to make an acceptance decision, one must calculate the targeted levels for less-preferred types, which may require the knowledge of the full vector X<sub>t</sub><sup>A<sub>p</sub></sup> of preferred customers in the system. Our numerical experiments in Section 7 suggest that a decentralized threshold policy—in which acceptance decisions are made locally at each resource based on its true (instead of corrected) head count—achieves, as well, logarithmic performance, but proving this seems challenging. Our proof for CHT relies on the fact that, in the auxiliary network, each resource and its matched less-preferred type can be studied in isolation. A general analysis of the decentralized policy might necessitate a complicated Lyapunov function that captures the interaction between resources. We conjecture, however, that logarithmic regret of the decentralized policy is provable, using decomposition arguments, for networks with a unique perfect matching such as those in Examples 3.1 and 3.2.

It is important that we benchmarked our policy against an LP upper bound (a deterministic counterpart). In the dynamic stochastic knapsack setting (finite horizon with nonreusable resources), the offline decision maker—one that sees the future realization of demand—was used as a benchmark; see Arlotto and Gurvich (2019) and Vera and Banerjee (2021). The offline objective value is a tighter upper bound than the LP. In that setting, the offline decision maker solves an LP with a random right-hand side. In the case of reusable resources, the offline problem—in which arrivals and service times are known to the decision maker—is a complicated dynamic program and, hence, difficult to use as a benchmark. We use the LP, instead. It is natural to ask whether (i) one can do better relative to the offline upper bound, and (ii) the nondegeneracy (overload) assumption can be removed if a tighter offline bound is used as a benchmark. Both questions were answered affirmatively in the dynamic stochastic knapsack setting. It is worth noting that one can have multiple offline problems. One simple offline version is the one in Equation (1) that we use in the proof of Lemma 3.1; it is reminiscent of the one used in the stochastic knapsack setting. That crude offline version suffices to prove that, with degeneracy, the gap from the LP is O(√N), but precisely because it does not capture the state constraints under nonpreemption, it is too loose. Indeed—with nondegeneracy—this offline version is O(1) from the LP, whereas we prove that no online policy can achieve sublogarithmic regret.

The development of tighter offline benchmarks that allow us to go beyond nondegeneracy remains desirable, especially given the strong results in the nonreusable case; see Vera et al. (2021).

There are several natural extensions to what we prove here. In our model, a request requires one unit of each of a set of resources. A natural extension would model requests in which the number of units is itself heterogeneous and random. The second question pertains to the regret when the network itself (the number of types and number of resources) scales rather than the number of units of each of (a finite set of) resources. A challenge here is to model the sequence of (growing) networks. The literature on flexibility might provide some clues here; see, for instance, Tsitsiklis and Xu (2017) and Van der Boor et al. (2018).

Answering these questions would further our understanding of dynamic resource allocation.

## Notation

Table 2 lists the notation that we use in the paper.

<table>
<thead>
<tr>
<th>Table 2. Notation</th>
<th></th>
</tr>
</thead>
<tbody>
<tr>
<td colspan="2"><strong>Problem setup</strong></td>
</tr>
<tr>
<td><em>i</em> ∈ [<em>n</em>] and <em>j</em> ∈ [<em>d</em>]</td>
<td>The set of customer types and resource types, respectively</td>
</tr>
<tr>
<td>λ<sub><em>i</em></sub><sup><em>N</em></sup> = <em>N</em>λ<sub><em>i</em></sub>, <em>q</em><sub><em>j</em></sub><sup><em>N</em></sup> = <em>N</em><em>q</em><sub><em>j</em></sub></td>
<td>Arrival rates and available resource quantities, where <em>N</em> is the scaling factor</td>
</tr>
<tr>
<td><em>A</em><sub><em>i</em></sub>(<em>j</em>)</td>
<td>The set of customer types that request a type <em>j</em> resource unit</td>
</tr>
<tr>
<td><em>S</em>(<em>i</em>)</td>
<td>The set of resource types requested by type <em>i</em> customers</td>
</tr>
<tr>
<td colspan="2"><strong>The LP solution under the overload condition</strong></td>
</tr>
<tr>
<td><em>R</em>* and <em>R</em>(<em>q</em>, λ/μ)</td>
<td>The optimal value of dynamic programming and of the LP relaxation</td>
</tr>
<tr>
<td>[<em>n</em>] = <em>A</em><sub><em>p</em></sub> ∪ <em>A</em><sub><em>lp</em></sub></td>
<td>Partition of the customer base: preferred types and less-preferred types</td>
</tr>
<tr>
<td><em>A</em><sub><em>p</em></sub>(<em>j</em>) = <em>A</em><sub><em>p</em></sub> ∩ <em>A</em>(<em>j</em>)</td>
<td>The set of preferred customer types that request a type <em>j</em> resource unit</td>
</tr>
<tr>
<td><em>A</em><sub><em>lp</em></sub>(<em>j</em>) = <em>A</em><sub><em>lp</em></sub> ∩ <em>A</em>(<em>j</em>)</td>
<td>The set of preferred customer types that request a type <em>j</em> resource unit</td>
</tr>
<tr>
<td colspan="2"><strong>CHT</strong></td>
</tr>
<tr>
<td>δ<sub><em>i</em></sub>, <em>i</em> ∈ <em>A</em><sub><em>lp</em></sub></td>
<td>Threshold coefficient for each less-preferred customer type</td>
</tr>
<tr>
<td><em>X</em><sub><em>t</em></sub></td>
<td>The number of customers in service customers at time <em>t</em></td>
</tr>
<tr>
<td><em>x</em><sub><em>i</em></sub>*(<em>X</em><sub><em>t</em></sub>)</td>
<td>The targeted number of type <em>i</em> (<em>i</em> ∈ <em>A</em><sub><em>lp</em></sub>) customers at time <em>t</em></td>
</tr>
<tr>
<td>Σ<sub><em>j</em></sub><sup><em>t</em></sup> = Σ<sub><em>i</em>∈<em>A</em>(<em>j</em>)</sub> <em>X</em><sub><em>i</em></sub><sup><em>t</em></sup></td>
<td>Number of customers using type <em>j</em> resources</td>
</tr>
<tr>
<td>Σ<sub><em>j</em></sub><sup>*,<em>t</em></sup> = σ<sub><em>j</em></sub>(<em>X</em><sup><em>t</em></sup>)</td>
<td>The corrected resource <em>j</em> head count process</td>
</tr>
</tbody>
</table>


---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2118                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

**Table 2.** (Continued)

<table>
<thead>
<tr>
<th>Markov chain analysis</th>
<th></th>
</tr>
</thead>
<tbody>
<tr>
<td>Q</td>
<td>The infinitesimal generator (of the relaxed policy π̃)</td>
</tr>
<tr>
<td>CHT, π̃</td>
<td>The corrected head count threshold policy and the relaxed policy</td>
</tr>
<tr>
<td>X<sup>t</sup>, X̃<sup>t</sup></td>
<td>The number of under-service customers under the two policies at time t</td>
</tr>
<tr>
<td>Π, Π̃</td>
<td>The stationary distribution of X<sup>t</sup> and X̃<sup>t</sup></td>
</tr>
<tr>
<td>X, X̃</td>
<td>The state space of the two policies</td>
</tr>
<tr>
<td>η</td>
<td>Absolute constant (that does not depend on N)</td>
</tr>
</tbody>
</table>

## Acknowledgments
The authors thank the associate editor and three referees for their constructive comments.

## Appendix A. Proofs of Lemmas
### A.1. Proofs for Lemmas in Section 3

**Proof of Lemma 3.2.** The feasible region of (LP) is the polyhedron

$$\{y ∈ \mathbb{R}^n : Ay ≤ q \text{ and } 0 ≤ y ≤ λ/μ\}.$$

Because of our assumed strict complementary slackness, all d resource constraints are tight (i.e., hold at equality). Furthermore, because the LP solution is, by assumption, nondegenerate, and there are n variables, there are exactly n linearly independent tight constraints at the (extreme point) optimal solution. Thus, there remain exactly n − d tight constraints among the demand constraints 0 ≤ y ≤ λ/μ, that is, exactly n − d variables with y<sub>i</sub><sup>*</sup> that are equal to either zero or λ<sub>i</sub>/μ<sub>i</sub>. As a result, there are exactly d variables with 0 < y<sub>i</sub><sup>*</sup> < λ<sub>i</sub>/μ<sub>i</sub> (corresponding to the less preferred types).

For the second statement of the lemma, consider the residual optimization problem for types i ∈ A<sub>lp</sub>. Specifically, let r<sub>A<sub>lp</sub></sub> and y<sub>A<sub>lp</sub></sub> be the suitable subvectors of r and y and A<sub>A<sub>lp</sub></sub> be the submatrix of A that has only the columns for i ∈ A<sub>lp</sub>. Because |A<sub>lp</sub>| = d, this is a d × d matrix. The residual LP is then given by

$$\max \quad (r/μ)'_{A_{lp}} y_{A_{lp}}$$
$$\text{s.t.} \quad A_{A_{lp}} y_{A_{lp}} ≤ q̃,$$
$$0 ≤ y_{A_{lp}} ≤ (λ/μ)_{A_{lp}},$$

where q̃ = q − A_{A_p} (λ/μ)_{A_p} is the residual capacity after allocation to the preferred types. This residual optimization problem has the unique (sub)solution y<sub>A<sub>lp</sub></sub><sup>*</sup>. The residual LP has 2d constraints. Because |A<sub>lp</sub>| = d and y<sub>i</sub><sup>*</sup> ∈ (0, λ<sub>i</sub>/μ<sub>i</sub>) for all i ∈ A<sub>lp</sub>, the optimal basis has all the decision variables as well as the slack variables for the demand constraints. The slacks for the capacity constraints are zero-valued and nonbasic. We conclude that (i) the solution for this residual problem is, as well, unique and nondegenerate, and (ii) the basis matrix is A<sub>lp</sub> itself and has linearly independent rows. □

**Proof of Lemma 3.1.** The proof is an adaptation of the proof of proposition 1 in Vera and Banerjee (2021). The difference here is that the right-hand side corresponds to the stationary count of requests instead of the total number of arrivals.

Fix a stationary policy and let X<sub>i</sub><sup>t</sup> be the number of type i customers in the system. Simple coupling shows that X<sub>1</sub><sup>t</sup>, ..., X<sub>n</sub><sup>t</sup> is bounded component-wise from above by Y<sub>1</sub><sup>t</sup>, ..., Y<sub>n</sub><sup>t</sup>, where Y<sub>i</sub><sup>t</sup> is the number of type i customers in an infinite server queue that accepts all arriving customers. The variables Y<sub>1</sub><sup>t</sup>, ..., Y<sub>n</sub><sup>t</sup> are independent, and as t ↑ ∞, Y<sub>i</sub><sup>t</sup> converges to a Poisson random variable with mean λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>.

In turn, an upper bound on the performance of any stationary policy is given by the expectation applied to (LP), in which the deterministic demand constraints λ<sup>N</sup>/μ are replaced with the random vector Y<sup>N</sup> = (Y<sub>i</sub><sup>N</sup>, i ∈ [n]); that is, let

$$\mathcal{R}(q^N, Y^N) := \begin{cases}
\max_{y∈\mathbb{R}^n} & r'y/μ \\
\text{s.t.} & Ay ≤ q^N, \\
& y ≤ Y^N.
\end{cases}$$

Then, by Jensen's inequality,

$$\mathbb{E}[\mathcal{R}(q^N, Y^N)] ≤ \mathcal{R}(q^N, λ^N/μ) = \begin{cases}
\max_{y∈\mathbb{R}^n} & r'y/μ \\
\text{s.t.} & Ay ≤ q^N, \\
& y ≤ λ^N/μ.
\end{cases}$$

The dual to the problem on the right-hand side (the deterministic upper bound) is given by

$$D[λ^N/μ] := \min \quad α'q^N + β'(λ^N/μ)$$
$$\text{s.t.} \quad α'A + β ≥ r/μ,$$
$$α ∈ \mathbb{R}_+^d, β ∈ \mathbb{R}_+^n.$$

Notice that, because q<sup>N</sup> = qN and λ<sup>N</sup> = Nλ, the dual solution does not depend on N. Let β = β<sup>1</sup> − β<sup>2</sup> and α = α<sup>1</sup> − α<sup>2</sup>. By the assumption of this lemma, β ≠ 0.

Arguing as in Vera and Banerjee (2021), we conclude that

$$\mathcal{R}(q^N, λ^N/μ) − \mathbb{E}[\mathcal{R}(q^N, Y^N)]$$
$$= \mathbb{E}[(λ^N/μ − Y)' β \mathbf{1}\{(λ^N/μ − Y^N)' β > 0\}]$$
$$= \sqrt{N}\mathbb{E}\left[\frac{1}{\sqrt{N}}(λ^N/μ − Y^N)' β \mathbf{1}\left\{\frac{1}{\sqrt{N}}(λ^N/μ − Y^N)' β > 0\right\}\right].$$

Recall that Y<sub>i</sub><sup>N</sup> ~ Poisson(λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>). By standard results, $\frac{1}{\sqrt{N}}(λ^N/μ − Y^N)' β \Rightarrow \hat{W}$, where $\hat{W}$ is a normal random variable with zero mean and variance (λ/μ)' β. By Fatou's lemma,

$$\lim_{N↑∞} \mathbb{E}\left[\frac{1}{\sqrt{N}}(λ^N/μ − Y^N)' β \mathbf{1}\left\{\frac{1}{\sqrt{N}}(λ^N/μ − Y^N)' β > 0\right\}\right]$$
$$≥ \mathbb{E}[\hat{W} \mathbf{1}\{\hat{W} > 0\}] > 0,$$

where the last inequality is a basic property of the normal random variable. We conclude that

$$\mathcal{R}(q^N, λ^N/μ) − \mathbb{E}[\mathcal{R}(q^N, Y^N)] = Ω(\sqrt{N}). \quad □$$
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                2119

## A.2. Proofs for Lemmas in Section 5

**Proof of Lemma 5.1.** The Markov chain (X̃<sup>t</sup>, t ≥ 0) is easily verified to be irreducible relative to the state space

$$\tilde{X} = \{x \in \mathbb{Z}_+^n : x_i \leq q_{ji}^N - \delta_i \log N + 1, \text{ for all } i \in A_{lp}\}.$$

For i ∈ A<sub>p</sub>, X̃<sub>i</sub><sup>t</sup> follows the law of an infinite server queue (independently of X<sub>j</sub><sup>t</sup>, j ≠ i), so we have that lim<sub>t↑∞</sub> E<sub>x</sub>[X<sub>i</sub><sup>t</sup>] = λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>. Because x<sub>i</sub> ≤ q<sub>ji</sub><sup>N</sup> - δ<sub>i</sub> log N for i ∈ A<sub>lp</sub>, we overall have

$$\lim \sup_{t↑∞} \sum_{i∈[n]} E_x[X_i^t] < ∞,$$

for any initial state x.

By Markov's inequality, there exists K such that lim inf<sub>t↑∞</sub> P{∑<sub>i∈[n]</sub> X<sub>i</sub><sup>t</sup> ≤ K} ≥ 1/2. For an irreducible chain on $\mathbb{Z}_+^n$, we can have only one of two outcomes: either lim<sub>t↑∞</sub>P<sub>x</sub>{∑<sub>i∈[n]</sub> X<sub>i</sub><sup>t</sup> = j} = 0 for all j or the chain is positive recurrent and has a unique stationary distribution (see corollary 4.7 in Asmussen 2003). We conclude that the chain is positive recurrent.

The concentration bound for types i ∈ A<sub>p</sub> is standard and follows immediately from the fact that the stationary distribution for these types is (independently of everything else) Poisson with mean λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub> = λ<sub>i</sub>N/μ<sub>i</sub>. For a Poisson random variable Z with mean ν, it is known that

$$P\{Z \geq \nu + x\} \leq \exp\left(-\frac{x^2}{2(\nu + x/3)}\right).$$

Plugging ν = λ<sub>i</sub>N/μ<sub>i</sub> and x = εN and applying a union bound, we obtain the bound in the lemma with m<sub>1</sub> = 2(n - d) and m<sub>2</sub> = min<sub>i∈A<sub>p</sub></sub> ε<sup>2</sup>/(2λ<sub>i</sub>/μ<sub>i</sub> + 2ε/3). □

**Proof of Lemma 5.2.** We use the following result for Markov chains; see, for example, proposition 3 in Glynn and Zeevi (2008).

**Lemma A.1.** *Suppose that Z = (Z<sup>t</sup> : t ≥ 0) is a nonexplosive continuous-time Markov chain on a state space Z with rate matrix Q and a stationary distribution Π. Then, for any function g : Z → ℝ for which ∑<sub>z∈Z</sub> Π(z)|Q(z,z)||g(z)| < ∞,*

$$E_\Pi[(Qg)(Z)] = 0.$$

Trivially,

$$|Q(x,x)| \leq \sum_{i∈[n]} (\lambda_i^N + \mu_i x_i) \leq \eta_1 N + \eta_2 \sum_{i∈A_p} x_i.$$

Here, we use the fact that, for i ∈ A<sub>lp</sub>, x<sub>i</sub> ≤ q<sub>ji</sub><sup>N</sup> = q<sub>ji</sub>N. Recalling the definition of σ<sub>j</sub>(x) in (10), we have that

$$|\sigma_{ji}(x) - \hat{q}_{ji}^N| \leq \sigma_{ji}(x) + \hat{q}_{ji}^N$$

$$\leq x_i + \sum_{k∈A_p} x_k + \sum_{k∈A_{lp}} x_k^*(x) + \hat{q}_{ji}^N$$

$$\leq x_i + \sum_{k∈A_p} x_k + \sum_{k∈A_{lp}} |(A_{lp}^{-1}(q^N - A_p x_{A_p}))_k| + \hat{q}_{ji}^N$$

$$\leq x_i + \eta_1 \sum_{k∈A_p} x_k + \eta_2 N$$

$$\leq \eta_3\left(N + \sum_{k∈A_p} x_k\right),$$

where we use the fact that x<sup>*</sup>(ζ) := A<sub>lp</sub><sup>-1</sup>(q<sup>N</sup> - A<sub>p</sub>ζ) (see (3)). In the last inequality, we use again the fact that x<sub>i</sub> ≤ q<sub>ji</sub><sup>N</sup> = q<sub>ji</sub>N for i ∈ A<sub>lp</sub>. Thus, recalling g<sub>i</sub><sup>θ</sup>(x) = e<sup>θ(σ<sub>ji</sub>(x) - q̂<sub>ji</sub><sup>N</sup>)</sup>,

$$\sum_x \Pi(x)|Q(x,x)|g_i^θ(x)$$

$$= E_{\tilde{\Pi}}[|Q(\tilde{X},\tilde{X})|g_i^θ(\tilde{X})] \leq \eta_5 E_{\tilde{\Pi}}\left[\left(N + \sum_{k∈A_p} \tilde{X}_k\right)e^{\eta_6 θ(N + \sum_{k∈A_p} \tilde{X}_k)}\right]$$

$$\leq \eta_5 \sqrt{E_{\tilde{\Pi}}\left[\left(N + \sum_{k∈A_p} \tilde{X}_k\right)^2\right]} \sqrt{E_{\tilde{\Pi}}\left[e^{2\eta_6 θ(N + \sum_{k∈A_p} \tilde{X}_k)}\right]},$$

where the last step is Hölder's inequality. For k ∈ A<sub>p</sub>, X̃<sub>k</sub> is, under Π̃, a Poisson random variable with mean λ<sub>k</sub><sup>N</sup>/μ<sub>k</sub> and X̃<sub>i</sub>, i ∈ A<sub>p</sub> are independent random variables; the random variable ∑<sub>k∈A<sub>p</sub></sub> X̃<sub>k</sub> is, under Π̃, a Poisson random variable with mean ∑<sub>k∈A<sub>p</sub></sub> λ<sub>k</sub><sup>N</sup>/μ<sub>k</sub>. In particular, E<sub>Π̃</sub>[(∑<sub>k∈A<sub>p</sub></sub> X̃<sub>k</sub>)<sup>2</sup>] < ∞ and, for all small enough θ, E<sub>Π̃</sub>[e<sup>η<sub>6</sub>θ ∑<sub>k∈A<sub>p</sub></sub> X̃<sub>k</sub></sup>] < ∞. We conclude that ∑<sub>x</sub> Π(x)|Q(x,x)|g<sub>i</sub><sup>θ</sup>(x) < ∞ as required. The conclusion then follows from Lemma A.1. □

**Proof of Lemma 5.3.** Recalling (4),

$$x_i^*(ζ) = y_i^* N + \sum_{l∈A_p} α_i^l(ζ_l - λ_l/μ_l), \quad i ∈ A_{lp},$$

where α<sub>i</sub><sup>l</sup> = [A<sub>lp</sub><sup>-1</sup>A<sub>p</sub>]<sub>i,l</sub>, i ∈ A<sub>lp</sub>, l ∈ A<sub>p</sub>, are real numbers.

Recall σ<sub>ji</sub>(x) as defined in (10) and g<sub>i</sub><sup>θ</sup>(x) = e<sup>θ(σ<sub>ji</sub>(x) - q̂<sub>ji</sub><sup>N</sup>)</sup>. We fix i and θ and drop both, writing g(x) instead of g<sub>i</sub><sup>θ</sup>(x). We have

$$(Qg)(x) = \begin{cases}
\lambda_i^N(g(x + e_i) - g(x)) + μ_i x_i(g(x - e_i) - g(x)) \\
\quad + \sum_{l∈A_p(j_i)} \lambda_l^N(g(x + e_l) - g(x)) \\
\quad + \sum_{l∈A_p(j_i)} μ_l x_l(g(x - e_l) - g(x)) \\
\quad + \sum_{l∈A_p} \lambda_l^N(g(x + α_i^l e_l) - g(x)) \\
\quad + \sum_{l∈A_p} μ_l x_l(g(x - α_i^l e_l) - g(x)) & \text{if } σ_{ji}(x) \leq \hat{q}_{ji}^N \\
\\
μ_i x_i(g(x - e_i) - g(x)) \\
\quad + \sum_{l∈A_p(j_i)} \lambda_l^N(g(x + e_l) - g(x)) \\
\quad + \sum_{l∈A_p(j_i)} μ_l x_l(g(x - e_l) - g(x)) \\
\quad + \sum_{l∈A_p} \lambda_l^N(g(x + α_i^l e_l) - g(x)) \\
\quad + \sum_{l∈A_p} μ_l x_l(g(x - α_i^l e_l) - g(x)) & \text{if } σ_{ji}(x) > \hat{q}_{ji}^N,
\end{cases}$$

where e<sub>l</sub> stands for the vector in ℝ<sup>n</sup> with one in the lth coordinate and zero everywhere else.

Let

$$β(x) := \frac{1}{g(x)}\left(\sum_{l∈A_p} \lambda_l^N(g(x + α_i^l e_l) - g(x)) + \sum_{l∈A_p} μ_l x_l(g(x - α_i^l e_l) - g(x))\right).$$
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2120                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

Then,

$$|\beta(x)| \leq 2\sum_{l \in A_p} \max\{|\lambda_l^N e^{\alpha_l \theta}| - \mu_l x_l|, |\lambda_l^N - \mu_l x_l e^{\alpha_l \theta}|\}.$$

(A.1)

Expanding on Q<sub>g</sub>, we have

$$(Q_g)(x) = \begin{cases}
-(1-e^{-\theta})\left(\sum_{l \in A_p(j_i) \cup \{i\}} (\lambda_l - e^{\theta} \mu_l x_l)\right) g(x) + \beta(x)g(x) & \text{if } \sigma_{j_i}(x) < \hat{q}_{j_i}^N \\
\sum_{l \in A_p(j_i) \cup \{i\}} (\lambda_l^N + x_l \mu_l)(e^{\theta} - 1) + \beta(x) & \text{if } \sigma_{j_i}(x) = \hat{q}_{j_i}^N \\
-(1-e^{-\theta})\left(x_i \mu_i + \sum_{l \in A_p(j_i)} (x_l \mu_l - e^{\theta} \lambda_l^N)\right) g(x) + \beta(x)g(x) & \text{if } \sigma_{j_i}(x) > \hat{q}_{j_i}^N
\end{cases}$$

(A.2)

We use in this derivation the fact that, when $\sigma_{j_i}(x) = \hat{q}_{j_i}^N$, $f(x) = 0$ and $g(x) = \exp(\theta f(x)) = 1$.

Choose $\varepsilon = (1 + \|A_{lp}^{-1} A_p\|_{\infty})^{-1} \min_{i \in A_{lp}} y_i^*/（16n) \leq \min_{i \in A_{lp}} y_i^*/(16n)$. For $x \in \Omega_{\varepsilon}^N$, choosing $\theta > 0$ small enough (dependent on $\varepsilon$ and $\alpha = \max_{i,l} |\alpha_{i,l}|$), we have

$$\sum_{k \in A_p} |e^{\theta} \lambda_k^N - \mu_k x_k| + \sum_{k \in A_p} |\lambda_k^N - e^{\theta} \mu_k x_k| \leq N \min_{i \in A_{lp}} y_i^*/8,$$

as well as

$$\beta(x) \leq N \min_{i \in A_{lp}} y_i^*/8.$$

(A.3)

Consider now the three cases $\sigma_{j_i}(x) > \hat{q}_{j_i}^N$, $\sigma_{j_i}(x) < \hat{q}_{j_i}^N$, and $\sigma_{j_i}(x) = \hat{q}_{j_i}^N$.

i. $\sigma_{j_i}(x) > \hat{q}_{j_i}^N$: For $x \in \Omega_{\varepsilon}^N$ with $\sigma_{j_i}(x) > \hat{q}_{j_i}^N$,

$$x_i = \sigma_{j_i}(x) - \sum_{k \in A_p(j_i)} x_k - \sum_{k \in A_{lp}(j_i) \setminus \{i\}} x_k^*$$

$$\geq \hat{q}_{j_i}^N - \sum_{k \in A_p(j_i)} x_k - \sum_{k \in A_{lp}(j_i) \setminus \{i\}} x_k^*.$$

Recall that $x^*(ζ) = y_{lp}^* N + A_{lp}^{-1} A_p[(\lambda^N/\mu)_{A_p} - ζ]$. Given $x \in \Omega_{\varepsilon}^N$, take $ζ = x_{A_p}$. Then, for $x \in \Omega_{\varepsilon}^N$, $\|(\lambda^N/\mu)_{A_p} - x_{A_p}\| \leq n\varepsilon N$ and $\|x^*(x_{A_p}) - y_{lp}^* N\| \leq \|A_{lp}^{-1} A_p\|_{\infty} n\varepsilon N$. Because

$$\hat{q}_{j_i}^N = \sum_{i \in A(j_i)} y_i^* N = \sum_{i \in A_p(j_i)} \lambda_i^N/\mu_i + \sum_{i \in A_{lp}(j_i)} y_i^* N,$$

we then have that

$$x_i \geq \hat{q}_{j_i}^N - \sum_{k \in A_p(j_i)} \lambda_k^N/\mu_k - [A_{lp}^{-1} A_p(\lambda^N/\mu)_{A_p}]_i$$

$$- (1 + \|A_{lp}^{-1} A_p\|_{\infty})n\varepsilon N$$

$$= y_i^* N - \delta_i \log N - (1 + \|A_{lp}^{-1} A_p\|_{\infty})n\varepsilon N.$$

Recall that, by choice, $(1 + \|A_{lp}^{-1} A_p\|_{\infty})n\varepsilon = \min_{i \in A_{lp}} y_i^*/16$. Taking $\theta > 0$ small enough, the term in parentheses in the last

row of (A.2) satisfies, for all N large,

$$x_i \mu_i + \sum_{k \in A_p(j_i)} (x_k \mu_k - e^{\theta} \lambda_k^N)$$

$$\geq \mu_i y_i^* N - \mu_i(1 + \|A_{lp}^{-1} A_p\|_{\infty})n\varepsilon N \geq \mu_i(y_i^*/4)N.$$

Using (A.3) we finally have, choosing $\theta$ smaller if needed, that

$$-(1-e^{-\theta})\left(x_i \mu_i + \sum_{k \in A_p(j_i)} (x_k \mu_k - e^{\theta} \lambda_k^N)\right) + \beta(x)$$

$$\leq -(1-e^{-\theta})\mu_i y_i^* N/4 + |\beta(x)| \leq -\eta_1 N.$$

ii. $\sigma_{j_i}(x) < \hat{q}_{j_i}^N$: For $x \in \Omega_{\varepsilon}^N$ with $\sigma_{j_i}(x) < \hat{q}_{j_i}^N$, we have, following similar reasoning, that

$$-(1-e^{-\theta})\left(\sum_{l \in A_p(j_i) \cup \{i\}} (\lambda_l - e^{\theta} \mu_l x_l)\right) + \beta(x) \leq -\eta_2 N.$$

iii. $\sigma_{j_i}(x) = \hat{q}_{j_i}^N$: Because $x_i \leq \hat{q}_{j_i}^N$ for $x \in \tilde{X}$ and $x_k \leq \lambda_k^N/\mu_k + \varepsilon N$ for all $x \in \Omega_{\varepsilon}^N$, $k \in A_p$, we have that

$$|Q_g(x)| \leq (e^{\theta} + 1) \sum_{k \in A_p(j_i) \cup \{i\}} (\lambda_k^N + x_k \mu_k) + \beta(x) \leq \eta_3 N.$$

Overall, we have the existence of $\eta_4, \eta_5 > 0$ such that

$$(Q_g)(x) \leq -\eta_4 Ng(x) + \eta_5 N, \text{ for } x \in \Omega_{\varepsilon}^N,$$

as stated, taking $m_1 = \eta_4, m_2 = \eta_5$. □

**Proof of Lemma 5.4.** This is a direct corollary of theorem 13 of Morris and Peres (2005). Equation (46) there states, for a chain that moves at rate 1 according to a transition matrix P, that

$$\tau(\varepsilon) \leq \int_{4\Pi^*}^{4/\varepsilon} \frac{8 du}{u\Phi^2(u)}.$$

Here, $\Phi(\cdot)$ is the conductance profile (which depends on P). If we can identify a uniform lower bound $\Phi(u) \geq \Phi > 0$ for all $u \in [4\Pi^*, 4/\varepsilon]$, then

$$\tau(\varepsilon) \leq \frac{8}{\Phi^2} \int_{4\Pi^*}^{4/\varepsilon} \frac{du}{u} = \frac{8}{\Phi^2} \log u\Big|_{4\Pi^*}^{4/\varepsilon} = \frac{8}{\Phi^2} \log \frac{1}{\varepsilon\Pi^*}.$$

For the chain moving at rate U, we need to multiply the bound by $U^{-1}$. □

**Proof of Lemma 5.5.** We first prove (22). Recall that the transition probability matrix P is induced by the generator matrix through $P = U^{-1}Q + I$, where

$$U = N\sum_{i \in [n]} \lambda_i + \max_{x \in X} \sum_{i \in [n]} \mu_i x_i \leq N\left(\sum_{i \in [n]} \lambda_i + \sum_{j \in [d]} q_j \max_{i \in A(j)} \mu_i\right)$$

is the uniformization constant. Define

$$c := \frac{\sum_{i \in [n]} \lambda_i + \sum_{j \in [d]} q_j \max_{i \in A(j)} \mu_i}{\min\{\min_{i \in [n]} \mu_i, \min_{i \in [n]} \lambda_i\}}.$$

Transitions between state $x \in X$ and $y \in X$ occur either through arrivals (at rate at least $\min_i \lambda_i^N = N \min_i \lambda_i$) or through service departures at a minimal rate of $\min_i \mu_i$. We
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                2121

then have that

$$\min_{x \neq y \in \mathcal{X}: P_{xy} > 0} P_{xy} \geq \frac{\min\{\min_{i \in [n]} \mu_i, N \min_{i \in [n]} \lambda_i\}}{U} \geq \frac{1}{cN}$$ (A.4)

There exist constants K<sub>1</sub>, K<sub>2</sub> > 0 that do not depend on N such that

$$K_1 N \leq \max_{x,y \in \mathcal{X}} \min\{n \geq 1 : P_{xy}^n > 0\} \leq K_2 N.$$

For the upper bound, take states x, y ∈ 𝒳. Then, there is a path from x to zero through x<sub>i</sub> service completions of type i. There is then a path from zero to y through y<sub>i</sub> arrivals of type i. The total length of this path is ∑<sub>i</sub> x<sub>i</sub> + ∑<sub>i</sub> y<sub>i</sub> ≤ 2 max<sub>j</sub> q<sub>j</sub><sup>N</sup>, so we can take K<sub>2</sub> = 2 max<sub>j</sub> q<sub>j</sub>. For the lower bound, take x = 0 ∈ ℝ<sup>n</sup> and y = ⌊y*N⌋ = (⌊y<sub>1</sub>*N⌋, ..., ⌊y<sub>n</sub>*N⌋). Then, it takes at least ∑<sub>i</sub> ⌊y<sub>i</sub>*N⌋ arrivals to transition from x to y, and for all N large enough, we can take K<sub>1</sub> = ½ ∑<sub>i</sub> ½y<sub>i</sub>*.

As a result, for every x, y ∈ 𝒳, we have using (A.4) that

$$P_{xy}^{K_2 N} \geq (cN)^{-K_2 N}.$$

For all m = 0, 1, 2, ..., we also have

$$P^{m+K_2 N}(x,y) = \sum_{z \in \mathcal{X}} P^m(x,z)P^{K_2 N}(z,y)$$

$$\geq (cN)^{-K_2 N} \sum_{z \in \mathcal{X}} P^m(x,z) = (cN)^{-K_2 N}.$$

In particular, regardless of the initial state x,

$$\Pi(y) = \lim_{m \to \infty} P^{m+K_2 N}(x,y) \geq (cN)^{-K_2 N} \text{ for all } y \in \mathcal{X}.$$

Here, we use the fact that the continuous-time chain and its discrete (uniformized) counterpart have the same stationary distribution.

We turn to (23). By definition, the conductance is given by

$$\Phi = \min_{S \subseteq \mathcal{X}: \Pi(S) \leq 1/2} \frac{\sum_{x \in S, y \in S^c} \Pi(x)P_{xy}}{\Pi(S)}.$$

Transitions in the Markov chain X<sup>t</sup> are such that, for y ≠ x, P<sub>xy</sub> > 0 if and only if ∑<sub>i∈[n]</sub> |x<sub>i</sub> - y<sub>i</sub>| = 1. Evidently, each state x has at least one such neighbor, so we know from (A.4) that

$$\sum_{x \in S, y \in S^c} \Pi(x)P_{xy} = \sum_{x \in S} \Pi(x) \sum_{y \in S^c} P_{xy} \geq \frac{\Pi(S)}{cN},$$

for any S ⊆ 𝒳. As a result, the conductance is lower bounded by Φ ≥ 1/(cN). □

## A.3. Proofs for Lemmas in Section 6

**Proof of Lemma 6.1.** We first prove that (27) implies (28). Recall that x<sub>i</sub><sup>N</sup> = E<sub>π*<sup>N</sup></sub>[X<sub>i</sub>] ≤ λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>. If Ax<sup>N</sup> ≤ q<sup>N</sup> - κ log N for all sufficiently large N and some κ > 0, then x<sub>i</sub><sup>N</sup> is feasible for (LP) with the right-hand side modified to the smaller capacity q<sup>N</sup> - κ log N = N(q - κ(log N)/N). Let γ be the dual value of the capacity constraint in (LP). By Assumption 3.1, the primal has a unique nondegenerate solution, and in turn, so does the dual. In turn, for all large enough N, γ is the same for the right-hand sides q and q - κ(log N)/N. We would then have that r'x ≤ ℛ(q<sup>N</sup> - κ log N, λ<sup>N</sup>/μ) ≤ ℛ(q<sup>N</sup>, λ<sup>N</sup>/μ) - γκ log N, contradicting (27).

Further, because there always exists a solution that satisfies strict complementarity, the (unique) primal and dual solution must satisfy strict complementarity. In particular, the dual

variables for the demand constraint for i ∈ A<sub>p</sub> are strictly positive. A similar argument to the preceding one implies that the optimal policy must have (29) for, otherwise,

if x<sub>i</sub> ≤ λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub> - κ log N, then r'x ≤ ℛ(q<sup>N</sup>, λ<sup>N</sup> - e<sub>i</sub>κ log N) ≤ ℛ(q<sup>N</sup>, λ<sup>N</sup>/μ) - γκ log N,

for some new γ > 0.

We conclude, then, that, if (27) holds, then the sequence of optimal policies must satisfy both (28) and (29). □

## Appendix B. The Choice of Thresholds

We consider here d = 1 (a single resource) and provide heuristic guidance toward the choice of the threshold coefficient δ applied to the less-preferred type. The premise here is that the choice of threshold must strike a balance between the reward collected from less-preferred types and the possible loss of reward from the rejection of preferred customers. The trade-off is nonlinear: decreasing the coefficient has (approximately) a linear effect on the reward from less-preferred customers but a nonlinear effect on the probability of rejecting preferred customers.

The following simple calculation is grounded in strong intuition, and the numerical evidence supports this intuition.

**Construction.** When all (except at most log N) servers are busy, approximately y<sub>i</sub>*N of them are occupied with type i customers. The total departure rate is then

$$\sum_i \mu_i y_i^* N = \sum_{l=1}^{i^*} \lambda_l^N + \mu_{i^*+1} \left(q^N - \sum_{l=1}^{i^*} \lambda_l^N/\mu_l\right),$$

where i* is as in Example 2.1.

The input rate when above the threshold (so that type i* + 1 is not accepted) is ∑<sub>l=1</sub><sup>i*</sup> λ<sub>l</sub>. Heuristically, when above the threshold, the total number in the system behaves as an M/M/1 queue with utilization

$$\rho = \frac{\sum_{l=1}^{i^*} \lambda_l^N}{\sum_{l=1}^{i^*} \lambda_l^N + \mu_{i^*+1} y_{i^*+1}^N} < 1.$$

Approximately ∑<sub>l=1</sub><sup>n</sup> μ<sub>l</sub> y<sub>l</sub>*N customers are served per unit of time out of a total of ∑<sub>l=1</sub><sup>n</sup> λ<sub>l</sub><sup>N</sup> arrivals. The fraction of customers being blocked/rejected is then approximately

$$p_{block} := \frac{\sum_{l=1}^n \lambda_l^N - \sum_{l=1}^n \mu_l y_l^* N}{\sum_{l=1}^n \lambda_l^N},$$

which is approximately the likelihood that all q<sup>N</sup> - K servers are busy, when K is the threshold. Thus, the probability that all servers are busy—at which point preferred requests are also blocked—is approximately

p<sub>block</sub> × ρ<sup>K</sup>,

when the threshold is K. We are then looking for K that minimizes

$$\left(\sum_{l=1}^{i^*} r_l \lambda_l^N\right) p_{block} \rho^K + r_{i^*+1} \mu_{i^*+1} K.$$

This is a convex function of K. Let β = log(1/ρ). Provided that β∑<sub>l=1</sub><sup>i*</sup> r<sub>l</sub> λ<sub>l</sub><sup>N</sup> > r<sub>i*+1</sub> μ<sub>i*+1</sub> (which holds for all ρ sufficiently large N), we have the optimal solution
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2122                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

## Figure B.1. (Color online) Log-Regret as a Function of the Threshold Coefficient δ for the Reward Vector r = (1, 1, 1) (Top) and r = (10, 5, 1) (Bottom)

<table>
<thead>
<tr><th colspan="2">Log-normalized Regret, N=500 (Top Left)</th></tr>
<tr><th>Threshold Coefficient, δ</th><th>Log-normalized Regret</th></tr>
</thead>
<tbody>
<tr><td>1</td><td>5.0</td></tr>
<tr><td>2</td><td>2.1</td></tr>
<tr><td>3</td><td>1.5</td></tr>
<tr><td>4</td><td>1.4</td></tr>
<tr><td>5</td><td>1.6</td></tr>
<tr><td>6</td><td>2.0</td></tr>
<tr><td>7</td><td>2.3</td></tr>
<tr><td>8</td><td>2.6</td></tr>
<tr><td>9</td><td>2.9</td></tr>
<tr><td>10</td><td>3.2</td></tr>
</tbody>
</table>

<table>
<thead>
<tr><th colspan="2">Log-normalized Regret, N=1000 (Top Right)</th></tr>
<tr><th>Threshold Coefficient, δ</th><th>Log-normalized Regret</th></tr>
</thead>
<tbody>
<tr><td>1</td><td>8.5</td></tr>
<tr><td>2</td><td>3.0</td></tr>
<tr><td>3</td><td>1.8</td></tr>
<tr><td>4</td><td>1.6</td></tr>
<tr><td>5</td><td>1.8</td></tr>
<tr><td>6</td><td>2.1</td></tr>
<tr><td>7</td><td>2.4</td></tr>
<tr><td>8</td><td>2.8</td></tr>
<tr><td>9</td><td>3.1</td></tr>
<tr><td>10</td><td>3.4</td></tr>
</tbody>
</table>

<table>
<thead>
<tr><th colspan="2">Log-normalized Regret, N=500 (Bottom Left) - Local threshold policy</th></tr>
<tr><th>Threshold Coefficient, δ</th><th>Log-normalized Regret</th></tr>
</thead>
<tbody>
<tr><td>3</td><td>9.0</td></tr>
<tr><td>4</td><td>4.2</td></tr>
<tr><td>5</td><td>2.6</td></tr>
<tr><td>6</td><td>2.4</td></tr>
<tr><td>7</td><td>2.5</td></tr>
<tr><td>8</td><td>2.7</td></tr>
<tr><td>9</td><td>2.9</td></tr>
<tr><td>10</td><td>3.2</td></tr>
</tbody>
</table>

<table>
<thead>
<tr><th colspan="2">Log-normalized Regret, N=1000 (Bottom Right) - Local threshold policy</th></tr>
<tr><th>Threshold Coefficient, δ</th><th>Log-normalized Regret</th></tr>
</thead>
<tbody>
<tr><td>3</td><td>10.2</td></tr>
<tr><td>4</td><td>4.5</td></tr>
<tr><td>5</td><td>2.7</td></tr>
<tr><td>6</td><td>2.4</td></tr>
<tr><td>7</td><td>2.5</td></tr>
<tr><td>8</td><td>2.7</td></tr>
<tr><td>9</td><td>3.0</td></tr>
<tr><td>10</td><td>3.3</td></tr>
</tbody>
</table>

$$K = \frac{1}{\beta} \log \left( \frac{\beta p_{block} \sum_{l=1}^{i^*} r_l \lambda_l^N}{r_{i^*+1} \mu_{i^*+1}} \right). \quad (B.1)$$

Because λ<sub>l</sub><sup>N</sup> = Nλ<sub>l</sub>, we have that

$$K = \frac{1}{\beta} \log N + \Gamma,$$

where Γ is the constant $\frac{1}{\beta} \log \left( \frac{\beta p_{block} \sum_{l=1}^{i^*} r_l \lambda_l}{r_{i^*+1} \mu_{i^*+1}} \right)$ that does not scale with N.

Notice that the closer that ρ is to one, the closer β is to zero and the larger the coefficient of log N has to be.

As a sanity check, we note that this matches the exact asymptotic coefficient derived in Morrison (2010, theorem 1) for the two-class model with equal service rates (μ<sub>1</sub> = μ<sub>2</sub>); our β is equal to −log σ there.

For a numerical illustration, we consider the single-resource queue with three types and the following parameters: λ = (1, 1, 1), μ = (1, 1/2, 1/3), r = (1, 1, 1), and q = 4. The optimal solution to the LP has

y<sup>*</sup> = (1, 2, 1), R(q, λ/μ) = 7/3.

For these parameters, we get K = 30.05 so that δ = 30.05/log N = 4.84) for N = 500 (corresponding) and K = 34.55 for N = 1,000 (so that δ = 34.55/log N = 5). In Figure B.1, top, we plot the log-regret as a function of K/log N (a proxy for the threshold coefficient). For both N = 500 and N = 1,000, we see that the threshold coefficient that minimizes regret in the simulation is δ = 4 (one below our recommendation), but 5 produces close performance. Indeed, all of 3–5 produce a similar performance.

To show that the threshold and its performance respond as expected to changes in rewards, we repeat the same experiment, but now with the reward vector r = (10, 5, 1). The LP solution has the same decision values and the larger objective value 46/3. The thresholds, as expected, increase because the preferred types are more valuable: for N = 500, the optimal threshold in (B.1) is K = 43.12, corresponding to a coefficient of approximately δ = 6.94. Again, our choice of the threshold produces regret very similar to the simulation-optimal threshold, which is 7; see Figure B.1, bottom. This is true also for N = 1,000 for which our heuristic recommends δ = 6.9.

## Appendix C. The Insufficiency of Static Policies

We consider the case of a single resource (d = 1). Our goal here is to show that logarithmic regret is not achievable with
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS                                                                                                2123

a static policy: for logarithmic regret, the accept/reject decision must be state dependent. A state-independent policy is characterized by a vector p = (p<sub>1</sub>, ..., p<sub>n</sub>) ∈ [0,1]<sup>n</sup> of probabilities. When a type i request arrives, it is accepted with probability p<sub>i</sub> if there are units of the resource available, and it is declined otherwise. We denote by π<sub>S</sub>(p) the static policy using p.

**Lemma C.1.** Suppose that d = 1 (single resource) and that Assumption 3.1 holds. Then, for any sequence of vectors p<sup>N</sup> ∈ [0,1]<sup>n</sup>,

$$R(q^N, λ^N/μ) - R^{π_S(p^N),N} = Ω(\sqrt{N}). \quad (C.1)$$

**Proof.** Fix the sequence {p<sup>N</sup>} and let X<sub>i</sub><sup>N</sup> be the steady-state number of type i customers under p<sub>i</sub><sup>N</sup> (and with arrival rate λ<sub>i</sub><sup>N</sup> and q<sup>N</sup> resource units). We let Π<sub>S</sub>(p<sup>N</sup>) be the stationary distribution under the policy π<sub>S</sub>(p<sup>N</sup>). The existence of a steady state under such a policy is straightforward.

Under such a static policy, the head count Σ<sup>N</sup> = Σ<sub>i∈[n]</sub>X<sub>i</sub><sup>N</sup> in the system has the stationary law of an M/G/q<sup>N</sup>/q<sup>N</sup> with arrival rate Σ<sub>i∈[n]</sub>λ<sub>i</sub><sup>N</sup>p<sub>i</sub><sup>N</sup>, q<sup>N</sup> servers, and the service time distribution is a mixture of exponential distributions with mean

$$\frac{1}{μ^N} := \frac{Σ_{i∈[n]}\frac{λ_i^N p_i^N}{μ_i}}{Σ_{i∈[n]}λ_i^N p_i^N}.$$

Requests can be accepted only when the head count is strictly below q<sup>N</sup>. By PASTA, type i requests have the (stationary) admission probability p<sub>i</sub><sup>N</sup>P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> < q<sup>N</sup>}. By Little's law, the number of type i customers in service is then x<sub>i</sub><sup>N</sup> = E<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>[X<sub>i</sub><sup>N</sup>] = (λ<sub>i</sub><sup>N</sup>/μ<sub>i</sub>)p<sub>i</sub><sup>N</sup>P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> < q<sup>N</sup>}. By nondegeneracy, we must have that either

$$x_i^N = \frac{λ_i^N}{μ_i} + o(\sqrt{N}), i ∈ A_p, \quad (C.2)$$

or that (C.1) holds; this is argued as in the proof of Lemma 6.1. Assume, then, that (C.2) holds. It must then be that p<sub>i</sub><sup>N</sup>P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> < q<sup>N</sup>} = 1 - o(1/√N) and, in particular, that both

1 - p<sub>i</sub><sup>N</sup> = o(1/√N), i ∈ A<sub>p</sub>, and

P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> = q<sup>N</sup>} = 1 - P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> < q<sup>N</sup>} = o(1/√N).

Consider a sequence of M/G/q<sup>N</sup>/q<sup>N</sup> queues with total arrival rate λ<sup>N</sup> = Σ<sub>i∈[n]</sub>λ<sub>i</sub><sup>N</sup>p<sub>i</sub><sup>N</sup>, mean service time 1/μ<sup>N</sup>. Then, it is known (e.g., Janssen et al. 2008) that

P<sub>Π<sub>S</sub>(p<sup>N</sup>)</sub>{Σ<sup>N</sup> = q<sup>N</sup>} = o(1/√N) if and only if

λ<sup>N</sup>/μ<sup>N</sup> = q<sup>N</sup> - Ω(√N).

In turn,

$$\sum_{i∈[n]} x_i^N = \sum_{i∈[n]} \frac{λ_i^N}{μ_i} p_i^N P_{Π_S(p^N)}\{Σ^N < q^N\}$$

$$≤ \sum_{i∈[n]} \frac{λ_i^N}{μ_i} p_i^N ≤ q^N - Ω(\sqrt{N}).$$

By nondegeneracy and following the argument in the proof of Lemma 6.1, we then have

$$R(q^N, λ^N/μ) - R^{π_S(p^N),N} = Ω(\sqrt{N}),$$

as stated. □

## Endnotes

<sup>1</sup> Take states x, y ∈ X, where X = X<sup>N</sup> ≡ {x ∈ Z<sub>+</sub><sup>n</sup> : Ax ≤ q<sup>N</sup>} ∩ {x ∈ Z<sub>+</sub><sup>n</sup> : x<sub>i</sub> ≤ q<sub>ji</sub><sup>N</sup> - δ log N, for all i ∈ A<sub>lp</sub>} is the state space of the chain. Then, there is a path from x to zero through x<sub>i</sub> service completions of type i. There is then a path from zero to y through y<sub>i</sub> arrivals of type i.

<sup>2</sup> Because X̃<sup>t</sup> has a strictly larger state space than X<sup>t</sup>, the latter cannot be, strictly speaking, initialized with Π̃; this initialization is formalized in Section 5.6.

<sup>3</sup> Because the state space is finite, under any deterministic stationary policy, there is at least one recurrent class. Suppose there are two (or more) recurrent classes. Because, from any state x ∈ X, there is a path—through consecutive service completions—to 0 ∈ X (the empty state), zero must be part of any recurring class. Hence, there can be at most one recurring class.

## References

Arlotto A, Gurvich I (2019) Uniformly bounded regret in the multisecretary problem. Stochastic Systems 9(3):231–260.

Asmussen S (2003) Applied Probability and Queues, vol. 2 (Springer, New York).

Baek J, Ma W (2022) Bifurcating constraints to improve approximation ratios for network revenue management with reusable resources. Oper. Res. 70(4):2226–2236.

Bean NG, Gibbens RJ, Zachary S (1995) Asymptotic analysis of single resource loss systems in heavy traffic, with applications to integrated networks. Adv. Appl. Probab. 27(1):273–292.

Bean NG, Gibbens RJ, Zachary S (1997) Dynamic and equilibrium behavior of controlled loss networks. Ann. Appl. Probab. 7(4): 873–885.

Besbes O, Elmachtoub AN, Sun Y (2021) Static pricing: Universal guarantees for reusable resources. Oper. Res. 70(2):1143–1152.

Bumpensanti P, Wang H (2020) A re-solving heuristic with uniformly bounded loss for network revenue management. Management Sci. 66(7):2993–3009.

Cao H, Hu J, Jiang C, Kumar T, Li T-H, Liu Y, Lu Y, et al. (2011) Onthe-mark: Integrated stochastic resource planning of human capital supply chains. Interfaces 41(5):414–435.

Chen Y, Levi R, Shi C (2017) Revenue management of reusable resources with advanced reservations. Production Oper. Management 26(5):836–859.

Dawande M, Feng Z, Janakiraman G (2021) On the structure of bottlenecks in processes. Management Sci. 67(6):3853–3870.

Feng Y, Niazadeh R, Saberi A (2024) Near-optimal Bayesian online assortment of reusable resources. Oper. Res. Forthcoming.

Gallego G, van Ryzin G (1997) A multiproduct dynamic pricing problem and its applications to network yield management. Oper. Res. 45(1):24–41.

Glynn P, Zeevi A (2008) Bounding stationary expectations of Markov processes. Markov Processes and Related Topics: A Festschrift for Thomas G. Kurtz, vol. 4 (Institute of Mathematical Statistics, Beachwood, OH), 195–214.

Gong X-Y, Goyal V, Iyengar GN, Simchi-Levi D, Udwani R, Wang S (2021) Online assortment optimization with reusable resources. Management Sci. 68(7):4772–4785.

Goyal V, Iyengar G, Udwani R (2020) Online allocation of reusable resources: Achieving optimal competitive ratio. Preprint, submitted February 6, https://arxiv.org/abs/2002.02430.

Gurvich I, Intelligent Automation (2018–2021) Dragons–dynamic resource allocation gains for operational networked sharing, Department of Defense (Army) STTR A18B-T007.

Gurvich I, Van Mieghem JA (2015) Collaboration and multitasking in networks: Architectures, bottlenecks, and capacity. Manufacturing Service Oper. Management 17(1):16–33.
---


Xie, Gurvich, and Küçükyavuz: Dynamic Allocation of Reusable Resources
2124                                                Operations Research, 2025, vol. 73, no. 4, pp. 2097–2124, © 2024 INFORMS

Hu J, Lu Y, Mojsilović A, Sharma M, Squillante MS (2010) Performance management of IT services delivery. Performance Evaluation Rev. 37(4):50–57.

Hui JY (2012) Switching and Traffic Theory for Integrated Broadband Networks, vol. 91 (Springer Science & Business Media, New York).

Hunt PJ, Kurtz TG (1994) Large loss networks. Stochastic Processes Appl. 53(2):363–378.

Hunt PJ, Laws CN (1997) Optimization via trunk reservation in single resource loss systems under heavy traffic. Ann. Appl. Probab. 7(4):1058–1079.

Iyengar G, Sigman K (2004) Exponential penalty function control of loss networks. Ann. Appl. Probab. 14(4):1698–1740.

Janssen AJEM, Van Leeuwaarden J, Zwart B (2008) Gaussian expansions and bounds for the Poisson distribution applied to the Erlang B formula. Adv. Appl. Probab. 40(1):122–143.

Jasin S, Kumar S (2012) A re-solving heuristic with bounded revenue loss for network revenue management with customer choice. Math. Oper. Res. 37(2):313–345.

Jia H, Shi C, Shen S (2024) Online learning and pricing for service systems with reusable resources. Oper. Res. 72(3):1203–1241.

Jung K, Lu Y, Shah DD, Sharma M, Squillante M (2019) Revisiting stochastic loss networks: Structures and approximations. Math. Oper. Res. 44(3):890–918.

Kelly FP (1986) Blocking probabilities in large circuit-switched networks. Adv. Appl. Probab. 18(2):473–505.

Kelly FP (1991) Loss networks. Ann. Appl. Probab. 1(3):319–378.

Key PB (1990) Optimal control and trunk reservation in loss networks. Probab. Engrg. Inform. Sci. 4(2):203–242.

Lei YM, Jasin S (2020) Real-time dynamic pricing for revenue management with reusable resources, advance reservation, and deterministic service time requirements. Oper. Res. 68(3):676–685.

Levi R, Radovanović A (2010) Provably near-optimal LP-based policies for revenue management in systems with reusable resources. Oper. Res. 58(2):503–507.

Lippman SA (1975) Applying a new device in the optimization of exponential queuing systems. Oper. Res. 23(4):687–710.

Miller BL (1969) A queueing reward system with several customer classes. Management Sci. 16(3):234–245.

Morris B, Peres Y (2005) Evolving sets, mixing and heat kernel bounds. Probab. Theory Related Fields 133(2):245–266.

Morrison JA (2010) Optimal trunk reservation for an overloaded link. Oper. Res. Lett. 38(6):499–501.

Motwani R, Raghavan P (1995) Randomized Algorithms (Cambridge University Press, Cambridge, UK).

Örmeci EL, van der Wal J (2006) Admission policies for a two class loss system with general interarrival times. Stochastic Models 22(1):37–53.

Örmeci EL, Burnetas A, van der Wal J (2001) Admission policies for a two class loss system. Stochastic Models 17(4):513–539.

Owen Z, Simchi-Levi D (2017) Price and assortment optimization for reusable resources. Preprint, submitted November 16, https://dx.doi.org/10.2139/ssrn.3070625.

Paschalidis I, Liu Y (2002) Pricing in multiservice loss networks: Static pricing, asymptotic optimality and demand substitution effects. IEEE/ACM Trans. Networking 10(3):425–438.

Paschalidis I, Tsitsiklis J (2000) Congestion-dependent pricing of network services. IEEE/ACM Trans. Networking 8(2):171–184.

Puhalskii AA, Reiman MI (1998) A critically loaded multirate link with trunk reservation. Queueing Systems 28(1–3):157–190.

Puterman ML (2014) Markov Decision Processes: Discrete Stochastic Dynamic Programming (John Wiley & Sons, New York).

Reiman MI (1991) Optimal trunk reservation for a critically loaded link. Jensen A, Iverson VB, eds. Teletraffic and Datatraffic: In a Period of Change (North Holland, Amsterdam), 247–252.

Reiman MI, Wang Q (2008) An asymptotically optimal policy for a quantity-based network revenue management problem. Math. Oper. Res. 33(2):257–282.

Ross S (1996) Stochastic Processes (John Wiley & Sons, New York).

Ross K, Tsang D (1989) Optimal circuit access policies in an ISDN environment: A Markov decision approach. IEEE Trans. Comm. 37(9):934–939.

Rusmevichientong P, Sumida M, Topaloglu H (2020) Dynamic assortment optimization for reusable products with random usage durations. Management Sci. 66(7):2820–2844.

Talluri KT, van Ryzin GJ (2004) The Theory and Practice of Revenue Management, International Series in Operations Research & Management Science, vol. 68 (Kluwer Academic Publishers, Boston).

Tsitsiklis JN, Xu K (2017) Flexible queueing architectures. Oper. Res. 65(5):1398–1413.

Tutte WT (1947) The factorization of linear graphs. J. London Math. Soc. s1-22(2):107–111.

Van der Boor M, Borst SC, Van Leeuwaarden JS, Mukherjee D (2018) Scalable load balancing in networked systems: Universality properties and stochastic coupling methods. Proc. Internat. Congress Math. (World Scientific, Singapore), 3893–3923.

Vanderbei RJ (1998) Linear Programming—Foundations and Extensions, Kluwer International Series in Operations Research and Management Service, vol. 4 (Kluwer, London).

Vera A, Banerjee S (2021) The Bayesian prophet: A low-regret framework for online decision making. Management Sci. 67(3): 1368–1391.

Vera A, Banerjee S, Gurvich I (2021) Online allocation and pricing: Constant regret via Bellman inequalities. Oper. Res. 69(3):821–840.

Williamson EL (1992) Airline network seat inventory control: Methodologies and revenue impacts. Unpublished PhD thesis, Massachusetts Institute of Technology, Cambridge, MA.

Xu H, Li B (2013) Dynamic cloud pricing for revenue maximization. IEEE Trans. Cloud Comput. 1(2):158–171.

**Xinchang Xie** holds a PhD in decision sciences from Duke University and was a post doc at Northwestern University. He is a senior operations research analyst at UPS with expertise in optimization, reinforcement learning, and machine learning.

**Itai Gurvich** is James Allen Professor of Operations in the Kellogg School of Management at Northwestern University. His research interests include performance analysis and optimization of processing networks and the theory of stochastic process approximations.

**Simge Küçükyavuz** is chair and David A. and Karen Richards Sachs Professor in the Industrial Engineering and Management Sciences Department at Northwestern University. She is an expert in mixed-integer, large-scale, and stochastic optimization. She is an INFORMS Fellow, and the recipient of the NSF CAREER Award and the INFORMS Computing Society Prize.
"""
Quick Integration Test for RL vs CHT Research Project

Simple test to verify that all components integrate correctly
without running full training loops.

Authors: <AUTHORS>
"""

import numpy as np
from core_rl_algorithms import RLAlgorithmFactory, RLConfig
from simulation_environment import EnvironmentFactory, ScenarioType
from analytical_benchmarks import AnalyticalBenchmarkFramework, PolicyParameters

def test_basic_integration():
    """Test basic integration of all components"""
    print("Testing Basic Integration...")
    
    # Test 1: Miller 1969 Environment + DQN
    print("\n1. Testing Miller 1969 Environment + DQN:")
    
    # Create environment
    miller_env = EnvironmentFactory.create_environment(
        ScenarioType.MILLER_1969,
        capacity=10,
        arrival_rates=[0.3, 0.2],
        service_rates=[1.0, 1.0],
        rewards=[1.0, 2.0],
        time_horizon=50.0  # Short for testing
    )
    
    # Get state and action dimensions
    test_state = miller_env.reset()
    state_dim = len(test_state)
    action_dim = miller_env.get_action_dim()
    
    print(f"  Environment state dim: {state_dim}, action dim: {action_dim}")
    
    # Create RL algorithm
    config = RLConfig(learning_rate=1e-3, batch_size=16)
    dqn_agent = RLAlgorithmFactory.create_algorithm('dqn', state_dim, action_dim, config)
    
    print(f"  DQN agent created successfully")
    
    # Test a few steps
    for step in range(5):
        action = dqn_agent.select_action(test_state, training=False)
        next_state, reward, done, info = miller_env.step(action)
        print(f"  Step {step+1}: Action={action}, Reward={reward:.2f}, Done={done}")
        
        if done:
            break
        test_state = next_state
    
    print("  ✓ Miller 1969 + DQN integration successful")
    
    # Test 2: Network Environment + PPO
    print("\n2. Testing Network Environment + PPO:")
    
    # Create network environment
    network_env = EnvironmentFactory.create_environment(
        ScenarioType.XIE_2024_CHT,
        resource_capacities=[8, 6, 10],
        customer_types=4,
        arrival_rates=[0.8, 0.6, 0.7, 0.5],
        service_rates=[1.0, 1.2, 0.8, 1.1],
        rewards=[2.0, 3.0, 1.5, 2.5],
        time_horizon=50.0  # Short for testing
    )
    
    # Get state and action dimensions
    test_state = network_env.reset()
    state_dim = len(test_state)
    action_dim = network_env.get_action_dim()
    
    print(f"  Environment state dim: {state_dim}, action dim: {action_dim}")
    
    # Create RL algorithm
    ppo_agent = RLAlgorithmFactory.create_algorithm('ppo', state_dim, action_dim, config)
    
    print(f"  PPO agent created successfully")
    
    # Test a few steps
    for step in range(5):
        action, _, _ = ppo_agent.select_action(test_state, training=False)
        next_state, reward, done, info = network_env.step(action)
        print(f"  Step {step+1}: Action={action}, Reward={reward:.2f}, Done={done}")
        
        if done:
            break
        test_state = next_state
    
    print("  ✓ Network Environment + PPO integration successful")
    
    # Test 3: Analytical Benchmarks
    print("\n3. Testing Analytical Benchmarks:")
    
    framework = AnalyticalBenchmarkFramework()
    
    # Miller policy
    miller_params = PolicyParameters(
        capacity=10,
        arrival_rates=np.array([0.3, 0.2]),
        service_rates=np.array([1.0, 1.0]),
        rewards=np.array([1.0, 2.0]),
        customer_types=2
    )
    
    miller_policy = framework.create_miller_1969_policy(miller_params)
    print(f"  Miller policy created: thresholds = {miller_policy.thresholds}")
    
    # Test policy action
    test_state = {'occupancy': 5}
    test_customer = {'customer_type': 1}
    action = miller_policy.get_action(test_state, test_customer)
    print(f"  Miller policy action for occupancy=5, customer_type=1: {action}")
    
    # CHT policy
    cht_params = PolicyParameters(
        capacity=np.array([8, 6, 10]),
        arrival_rates=np.array([0.8, 0.6, 0.7, 0.5]),
        service_rates=np.array([1.0, 1.2, 0.8, 1.1]),
        rewards=np.array([2.0, 3.0, 1.5, 2.5]),
        customer_types=4,
        resource_types=3
    )
    
    cht_policy = framework.create_cht_policy(cht_params)
    print(f"  CHT policy created: thresholds = {cht_policy.cht_thresholds}")
    
    # Test policy action
    test_state = {'resource_occupancy': np.array([3, 2, 4])}
    test_customer = {'customer_type': 1}
    action = cht_policy.get_action(test_state, test_customer)
    print(f"  CHT policy action for resource_occupancy=[3,2,4], customer_type=1: {action}")
    
    print("  ✓ Analytical benchmarks integration successful")
    
    return True

def test_training_loop():
    """Test a short training loop"""
    print("\n4. Testing Short Training Loop:")
    
    # Create simple environment
    env = EnvironmentFactory.create_environment(
        ScenarioType.MILLER_1969,
        capacity=5,  # Smaller for faster testing
        arrival_rates=[0.5, 0.3],
        service_rates=[1.0, 1.0],
        rewards=[1.0, 2.0],
        time_horizon=20.0  # Very short
    )
    
    # Create agent
    state = env.reset()
    state_dim = len(state)
    action_dim = env.get_action_dim()
    
    config = RLConfig(learning_rate=1e-3, batch_size=8, epsilon_decay=0.99)
    agent = RLAlgorithmFactory.create_algorithm('dqn', state_dim, action_dim, config)
    
    print(f"  Training DQN agent for 3 episodes...")
    
    total_rewards = []
    
    for episode in range(3):
        state = env.reset()
        episode_reward = 0
        steps = 0
        
        while steps < 50:  # Limit steps per episode
            action = agent.select_action(state, training=True)
            next_state, reward, done, info = env.step(action)
            
            # Store experience
            agent.store_experience(state, action, reward, next_state, done)
            
            # Train if enough experience
            if len(agent.replay_buffer) >= agent.config.batch_size:
                loss_info = agent.train_step()
            
            episode_reward += reward
            state = next_state
            steps += 1
            
            if done:
                break
        
        total_rewards.append(episode_reward)
        print(f"    Episode {episode+1}: Reward = {episode_reward:.2f}, Steps = {steps}")
    
    print(f"  Average reward: {np.mean(total_rewards):.2f}")
    print("  ✓ Training loop integration successful")
    
    return True

if __name__ == "__main__":
    print("Integration Test for RL vs CHT Research Project")
    print("=" * 50)
    
    try:
        # Test basic integration
        test_basic_integration()
        
        # Test training loop
        test_training_loop()
        
        print("\n" + "=" * 50)
        print("🎉 ALL INTEGRATION TESTS PASSED! 🎉")
        print("All components are properly integrated and working.")
        print("Ready for full experimental execution.")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\nIntegration issues need to be resolved before proceeding.")

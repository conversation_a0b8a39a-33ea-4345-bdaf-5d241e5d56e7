# Mathematical Validation Framework Configuration
# RL vs CHT Research Project

# Global validation settings
global_settings:
  default_tolerance: 1.0e-12
  high_precision_tolerance: 1.0e-15
  warning_threshold: 1.0e-10
  max_iterations: 10000
  random_seed: 42

# Numerical precision settings
precision:
  decimal_places: 50
  floating_point_precision: "double"
  use_arbitrary_precision: true
  precision_monitoring: true

# Validation test categories
validation_categories:
  reward_calculations:
    enabled: true
    tolerance: 1.0e-12
    test_cases:
      - name: "linear_reward"
        function_type: "linear"
        parameters: [1, 2, 3, 4, 5]
      - name: "exponential_reward"
        function_type: "exponential"
        parameters: [0.1, 0.5, 1.0, 2.0]
      - name: "logarithmic_reward"
        function_type: "logarithmic"
        parameters: [1, 2, 5, 10]
  
  regret_calculations:
    enabled: true
    tolerance: 1.0e-12
    test_cases:
      - name: "basic_regret"
        scenarios: ["positive_regret", "zero_regret", "negative_regret"]
      - name: "normalized_regret"
        scenarios: ["standard_case", "zero_optimal", "large_numbers"]
  
  backcast_validation:
    enabled: true
    tolerance: 1.0e-10
    test_cases:
      - name: "single_resource"
        capacity: [1, 2, 5, 10]
        customer_types: [2, 3, 5]
      - name: "multi_resource"
        resources: [2, 3, 5]
        capacity_per_resource: [1, 2, 3]
      - name: "time_constrained"
        time_horizons: [10, 50, 100]
  
  statistical_validation:
    enabled: true
    tolerance: 1.0e-10
    test_cases:
      - name: "confidence_intervals"
        confidence_levels: [0.90, 0.95, 0.99]
        sample_sizes: [30, 100, 1000]
      - name: "hypothesis_testing"
        alpha_levels: [0.01, 0.05, 0.10]
      - name: "effect_size_calculation"
        methods: ["cohen_d", "eta_squared", "r_squared"]

# Cross-validation settings
cross_validation:
  enabled: true
  methods:
    - "independent_implementation"
    - "analytical_verification"
    - "numerical_integration"
    - "monte_carlo_verification"
  
  independent_implementation:
    languages: ["python", "numpy", "scipy"]
    libraries: ["decimal", "fractions", "mpmath"]
  
  analytical_verification:
    known_solutions:
      - "single_server_queue"
      - "trunk_reservation"
      - "cmu_rule"
    
  monte_carlo_verification:
    sample_sizes: [1000, 10000, 100000]
    confidence_level: 0.95

# Error handling and reporting
error_handling:
  log_level: "INFO"
  log_file: "validation.log"
  detailed_logging: true
  
  error_categories:
    - "numerical_precision"
    - "algorithm_convergence"
    - "input_validation"
    - "output_verification"
  
  reporting:
    generate_html_report: true
    generate_pdf_report: false
    include_plots: true
    plot_format: "png"

# Performance monitoring
performance:
  monitor_execution_time: true
  memory_usage_tracking: true
  
  benchmarks:
    reward_calculation_time: 1.0e-6  # seconds
    regret_calculation_time: 1.0e-6  # seconds
    backcast_calculation_time: 1.0e-3  # seconds
    statistical_calculation_time: 1.0e-4  # seconds
  
  optimization:
    use_vectorization: true
    parallel_processing: false
    cache_results: true

# Test data generation
test_data:
  random_seed: 42
  
  reward_functions:
    linear:
      coefficients: [0.5, 1.0, 1.5, 2.0, 3.0]
      ranges: [[0, 10], [0, 100], [0, 1000]]
    
    exponential:
      decay_rates: [0.1, 0.5, 1.0, 2.0]
      base_values: [1, 2, 5, 10]
    
    polynomial:
      degrees: [2, 3, 4]
      coefficients: [[1, 1], [1, 1, 1], [1, 1, 1, 1]]
  
  network_configurations:
    simple:
      resources: 1
      customer_types: [2, 3, 5]
      arrival_rates: [0.5, 1.0, 1.5]
    
    medium:
      resources: [2, 3]
      customer_types: [3, 5, 8]
      arrival_rates: [1.0, 2.0, 3.0]
    
    complex:
      resources: [5, 10]
      customer_types: [10, 15, 20]
      arrival_rates: [5.0, 10.0, 15.0]

# Validation schedules
schedules:
  continuous_validation:
    enabled: true
    frequency: "on_code_change"
  
  nightly_validation:
    enabled: true
    time: "02:00"
    comprehensive: true
  
  weekly_validation:
    enabled: true
    day: "sunday"
    time: "01:00"
    full_suite: true

# Integration with research workflow
research_integration:
  phase_checkpoints:
    phase_0: ["basic_arithmetic", "precision_monitoring"]
    phase_1: ["literature_validation", "reference_implementations"]
    phase_2: ["experimental_design_validation", "backcast_implementation"]
    phase_3: ["algorithm_validation", "simulation_verification"]
    phase_4: ["experimental_validation", "statistical_verification"]
    phase_5: ["analysis_validation", "publication_verification"]
  
  milestone_validation:
    required_pass_rate: 0.95
    critical_tests: ["reward_calculation", "regret_calculation", "backcast_validation"]
    
  documentation_requirements:
    validation_report: true
    test_coverage_report: true
    performance_report: true

# External tool integration
external_tools:
  pytest:
    enabled: true
    config_file: "pytest.ini"
    coverage_threshold: 0.90
  
  numpy_testing:
    enabled: true
    assert_functions: ["assert_allclose", "assert_array_equal"]
  
  scipy_testing:
    enabled: true
    statistical_tests: true
  
  symbolic_computation:
    sympy: true
    mathematica: false
    maple: false

# Quality assurance
quality_assurance:
  code_review:
    required: true
    reviewers: 2
    automated_checks: true
  
  documentation:
    docstring_coverage: 0.95
    type_hints: true
    examples_required: true
  
  version_control:
    tag_releases: true
    changelog: true
    backup_frequency: "daily"

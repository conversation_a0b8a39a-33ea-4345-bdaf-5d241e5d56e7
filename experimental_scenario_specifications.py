"""
Experimental Scenario Specifications for RL vs CHT Research Project

Comprehensive specifications for all experimental scenarios across three tiers:
- Tier 1: Known Optimal Solutions (<PERSON> 1969, <PERSON><PERSON><PERSON> 1975)
- Tier 2: CHT Policy Comparisons (<PERSON><PERSON> et al. 2024)
- Tier 3: Backcast Analysis for Complex Scenarios

Each scenario is rigorously defined with mathematical parameters, validation criteria,
and expected outcomes to ensure reproducible and verifiable results.

Authors: <AUTHORS>
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import yaml
from abc import ABC, abstractmethod

class ScenarioTier(Enum):
    """Experimental scenario tiers"""
    TIER_1_KNOWN_OPTIMAL = "tier_1_known_optimal"
    TIER_2_CHT_COMPARISON = "tier_2_cht_comparison"
    TIER_3_BACKCAST_ANALYSIS = "tier_3_backcast_analysis"

class ValidationLevel(Enum):
    """Validation rigor levels"""
    BASIC = "basic"
    STANDARD = "standard"
    RIGOROUS = "rigorous"
    COMPREHENSIVE = "comprehensive"

@dataclass
class SystemParameters:
    """System-level parameters for scenarios"""
    capacity: np.ndarray
    arrival_rates: np.ndarray
    service_rates: np.ndarray
    rewards: np.ndarray
    customer_types: int
    resource_types: int
    time_horizon: float
    random_seed: int = 42

@dataclass
class ValidationCriteria:
    """Validation criteria for experimental scenarios"""
    convergence_tolerance: float
    minimum_episodes: int
    statistical_significance_level: float
    confidence_interval_level: float
    effect_size_threshold: float
    validation_level: ValidationLevel

@dataclass
class ExpectedOutcome:
    """Expected outcomes for scenario validation"""
    optimal_policy_structure: str
    expected_regret_bound: float
    convergence_episodes_range: Tuple[int, int]
    performance_ratio_range: Tuple[float, float]
    key_insights: List[str]

@dataclass
class ExperimentalScenario:
    """Complete specification for an experimental scenario"""
    scenario_id: str
    tier: ScenarioTier
    name: str
    description: str
    mathematical_formulation: str
    system_parameters: SystemParameters
    validation_criteria: ValidationCriteria
    expected_outcome: ExpectedOutcome
    implementation_notes: List[str]
    literature_reference: str
    estimated_runtime_minutes: int

class ScenarioSpecificationFramework:
    """Framework for managing experimental scenario specifications"""
    
    def __init__(self):
        self.scenarios: Dict[str, ExperimentalScenario] = {}
        self._create_tier_1_scenarios()
        self._create_tier_2_scenarios()
        self._create_tier_3_scenarios()
    
    def _create_tier_1_scenarios(self):
        """Create Tier 1 scenarios with known optimal solutions"""
        
        # Scenario T1-1: Miller (1969) Trunk Reservation
        miller_scenario = ExperimentalScenario(
            scenario_id="T1-1-MILLER-1969",
            tier=ScenarioTier.TIER_1_KNOWN_OPTIMAL,
            name="Miller (1969) Trunk Reservation Validation",
            description="Single-server system with two customer classes where RL must discover optimal trunk reservation policy",
            mathematical_formulation="""
            Single-server queueing system with:
            - Customer classes: i ∈ {1, 2} with λ₁ = 0.3, λ₂ = 0.2
            - Service rates: μ₁ = μ₂ = 1.0 (exponential service)
            - Rewards: r₁ = 1.0, r₂ = 2.0
            - Capacity: C = 10
            - Optimal policy: Reserve 2 units for class 2 (thresholds: T₁ = 8, T₂ = 10)
            """,
            system_parameters=SystemParameters(
                capacity=np.array([10]),
                arrival_rates=np.array([0.3, 0.2]),
                service_rates=np.array([1.0, 1.0]),
                rewards=np.array([1.0, 2.0]),
                customer_types=2,
                resource_types=1,
                time_horizon=1000.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-3,
                minimum_episodes=1000,
                statistical_significance_level=0.01,
                confidence_interval_level=0.99,
                effect_size_threshold=0.1,
                validation_level=ValidationLevel.RIGOROUS
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Threshold policy with T₁=8, T₂=10",
                expected_regret_bound=0.05,
                convergence_episodes_range=(800, 1200),
                performance_ratio_range=(0.95, 1.0),
                key_insights=[
                    "RL should discover threshold structure without prior knowledge",
                    "Convergence should be monotonic after initial exploration",
                    "Policy should match analytical optimum within tolerance"
                ]
            ),
            implementation_notes=[
                "Use discrete state space representation",
                "Implement exact Miller (1969) system dynamics",
                "Validate against analytical solution from original paper",
                "Monitor threshold discovery process"
            ],
            literature_reference="Miller, B. L. (1969). A queueing reward system with several customer classes. Management Science, 16(3), 234-245.",
            estimated_runtime_minutes=30
        )
        
        # Scenario T1-2: Lippman (1975) cμ Rule
        lippman_scenario = ExperimentalScenario(
            scenario_id="T1-2-LIPPMAN-1975",
            tier=ScenarioTier.TIER_1_KNOWN_OPTIMAL,
            name="Lippman (1975) cμ Rule Validation",
            description="Multi-class queueing system where RL must discover optimal cμ priority ordering",
            mathematical_formulation="""
            M/M/1 queueing system with:
            - Customer classes: i ∈ {1, 2, 3}
            - Arrival rates: λ = [0.2, 0.3, 0.1]
            - Service rates: μ = [1.0, 2.0, 0.5]
            - Rewards: r = [1.0, 1.5, 3.0]
            - cμ values: [1.0, 3.0, 1.5] → Priority order: 2, 3, 1
            - Optimal policy: Serve highest cμ customer first
            """,
            system_parameters=SystemParameters(
                capacity=np.array([1]),  # Single server
                arrival_rates=np.array([0.2, 0.3, 0.1]),
                service_rates=np.array([1.0, 2.0, 0.5]),
                rewards=np.array([1.0, 1.5, 3.0]),
                customer_types=3,
                resource_types=1,
                time_horizon=800.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-3,
                minimum_episodes=800,
                statistical_significance_level=0.01,
                confidence_interval_level=0.99,
                effect_size_threshold=0.1,
                validation_level=ValidationLevel.RIGOROUS
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Priority ordering: Class 2 > Class 3 > Class 1",
                expected_regret_bound=0.03,
                convergence_episodes_range=(600, 1000),
                performance_ratio_range=(0.97, 1.0),
                key_insights=[
                    "RL should discover cμ priority ordering",
                    "Priority should be consistent with cμ values",
                    "Performance should match analytical optimum"
                ]
            ),
            implementation_notes=[
                "Implement exact M/M/1 dynamics",
                "Track priority ordering discovery",
                "Validate against Lippman (1975) analytical results",
                "Monitor queue length dynamics"
            ],
            literature_reference="Lippman, S. A. (1975). Applying a new device in the optimization of exponential queuing systems. Operations Research, 23(4), 687-710.",
            estimated_runtime_minutes=25
        )
        
        self.scenarios[miller_scenario.scenario_id] = miller_scenario
        self.scenarios[lippman_scenario.scenario_id] = lippman_scenario
    
    def _create_tier_2_scenarios(self):
        """Create Tier 2 scenarios comparing against CHT policy"""
        
        # Scenario T2-1: CHT Overloaded Network
        cht_overloaded = ExperimentalScenario(
            scenario_id="T2-1-CHT-OVERLOADED",
            tier=ScenarioTier.TIER_2_CHT_COMPARISON,
            name="CHT Policy Comparison - Overloaded Network",
            description="Network resource allocation in overloaded regime comparing RL against CHT policy",
            mathematical_formulation="""
            Network with:
            - Resources: R = 3 types with capacities C = [8, 6, 10]
            - Customer types: K = 4 with arrival rates λ = [0.8, 0.6, 0.7, 0.5]
            - Service rates: μ = [1.0, 1.2, 0.8, 1.1]
            - Resource requirements: A ∈ R^{K×R}
            - Overload condition: ρ = Σᵢ λᵢ/μᵢ > 1.2
            - CHT thresholds calculated per Xie et al. (2024)
            """,
            system_parameters=SystemParameters(
                capacity=np.array([8, 6, 10]),
                arrival_rates=np.array([0.8, 0.6, 0.7, 0.5]),
                service_rates=np.array([1.0, 1.2, 0.8, 1.1]),
                rewards=np.array([2.0, 3.0, 1.5, 2.5]),
                customer_types=4,
                resource_types=3,
                time_horizon=2000.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-2,
                minimum_episodes=2000,
                statistical_significance_level=0.05,
                confidence_interval_level=0.95,
                effect_size_threshold=0.2,
                validation_level=ValidationLevel.COMPREHENSIVE
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Network-aware threshold policy",
                expected_regret_bound=0.1,
                convergence_episodes_range=(1500, 2500),
                performance_ratio_range=(0.9, 1.1),
                key_insights=[
                    "RL should match or exceed CHT performance",
                    "Network coordination should emerge naturally",
                    "Robustness to parameter uncertainty should be demonstrated"
                ]
            ),
            implementation_notes=[
                "Implement exact Xie et al. (2024) CHT algorithm",
                "Ensure overload condition is maintained",
                "Track corrected head count calculations",
                "Monitor network coordination emergence"
            ],
            literature_reference="Xie, X., Gurvich, I., & Küçükyavuz, S. (2024). Dynamic allocation of reusable resources: Logarithmic regret in overloaded networks. Operations Research, 73(4), 2097-2124.",
            estimated_runtime_minutes=60
        )
        
        # Scenario T2-2: CHT Underloaded Network
        cht_underloaded = ExperimentalScenario(
            scenario_id="T2-2-CHT-UNDERLOADED",
            tier=ScenarioTier.TIER_2_CHT_COMPARISON,
            name="CHT Policy Comparison - Underloaded Network",
            description="Network resource allocation in underloaded regime testing RL adaptability",
            mathematical_formulation="""
            Network with:
            - Resources: R = 3 types with capacities C = [12, 10, 15]
            - Customer types: K = 4 with arrival rates λ = [0.4, 0.3, 0.35, 0.25]
            - Service rates: μ = [1.0, 1.2, 0.8, 1.1]
            - Underload condition: ρ = Σᵢ λᵢ/μᵢ < 0.8
            - CHT may be suboptimal in underloaded regime
            """,
            system_parameters=SystemParameters(
                capacity=np.array([12, 10, 15]),
                arrival_rates=np.array([0.4, 0.3, 0.35, 0.25]),
                service_rates=np.array([1.0, 1.2, 0.8, 1.1]),
                rewards=np.array([2.0, 3.0, 1.5, 2.5]),
                customer_types=4,
                resource_types=3,
                time_horizon=1500.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-2,
                minimum_episodes=1500,
                statistical_significance_level=0.05,
                confidence_interval_level=0.95,
                effect_size_threshold=0.15,
                validation_level=ValidationLevel.STANDARD
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Adaptive policy exploiting underload",
                expected_regret_bound=0.08,
                convergence_episodes_range=(1000, 2000),
                performance_ratio_range=(1.0, 1.2),
                key_insights=[
                    "RL should outperform CHT in underloaded regime",
                    "Adaptive behavior should emerge",
                    "Resource utilization should be optimized"
                ]
            ),
            implementation_notes=[
                "Compare against CHT baseline",
                "Monitor adaptation to underload conditions",
                "Track resource utilization efficiency",
                "Validate performance improvement over CHT"
            ],
            literature_reference="Xie, X., Gurvich, I., & Küçükyavuz, S. (2024). Dynamic allocation of reusable resources: Logarithmic regret in overloaded networks. Operations Research, 73(4), 2097-2124.",
            estimated_runtime_minutes=45
        )
        
        self.scenarios[cht_overloaded.scenario_id] = cht_overloaded
        self.scenarios[cht_underloaded.scenario_id] = cht_underloaded
    
    def _create_tier_3_scenarios(self):
        """Create Tier 3 scenarios for backcast analysis"""
        
        # Scenario T3-1: Complex Network Topology
        complex_network = ExperimentalScenario(
            scenario_id="T3-1-COMPLEX-NETWORK",
            tier=ScenarioTier.TIER_3_BACKCAST_ANALYSIS,
            name="Complex Network Topology - Backcast Analysis",
            description="Complex network with interdependent resources where no analytical solution exists",
            mathematical_formulation="""
            Complex network with:
            - Mesh topology with 8 nodes, 4 resource types
            - Customer types: K = 6 with heterogeneous requirements
            - Non-exponential service times: Gamma(α=2, β=2)
            - Dynamic topology changes during operation
            - Resource dependencies: A_ij depends on network state
            - No known analytical solution
            """,
            system_parameters=SystemParameters(
                capacity=np.array([6, 8, 5, 10]),
                arrival_rates=np.array([0.3, 0.4, 0.2, 0.35, 0.25, 0.3]),
                service_rates=np.array([0.8, 1.0, 1.2, 0.9, 1.1, 0.7]),
                rewards=np.array([3.0, 2.5, 4.0, 1.8, 3.2, 2.8]),
                customer_types=6,
                resource_types=4,
                time_horizon=3000.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-2,
                minimum_episodes=3000,
                statistical_significance_level=0.05,
                confidence_interval_level=0.95,
                effect_size_threshold=0.3,
                validation_level=ValidationLevel.COMPREHENSIVE
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Complex adaptive policy",
                expected_regret_bound=0.15,
                convergence_episodes_range=(2000, 4000),
                performance_ratio_range=(0.8, 0.95),
                key_insights=[
                    "RL should handle complexity where analytical methods fail",
                    "Backcast analysis provides performance bounds",
                    "Adaptive behavior should emerge for complex scenarios"
                ]
            ),
            implementation_notes=[
                "Implement backcast optimization for performance bounds",
                "Use dynamic programming with perfect hindsight",
                "Track adaptation to topology changes",
                "Validate against backcast optimal"
            ],
            literature_reference="Novel scenario - no existing analytical solution",
            estimated_runtime_minutes=90
        )
        
        # Scenario T3-2: Non-Exponential Service Times
        non_exponential = ExperimentalScenario(
            scenario_id="T3-2-NON-EXPONENTIAL",
            tier=ScenarioTier.TIER_3_BACKCAST_ANALYSIS,
            name="Non-Exponential Service Times - Backcast Analysis",
            description="System with general service time distributions where Markovian analysis fails",
            mathematical_formulation="""
            System with:
            - Service times: Weibull(k=1.5, λ=1.0), Lognormal(μ=0, σ=0.5)
            - Customer abandonment with patience ~ Exponential(0.1)
            - Finite population effects
            - Time-varying arrival patterns
            - No memoryless property - analytical intractability
            """,
            system_parameters=SystemParameters(
                capacity=np.array([8, 6]),
                arrival_rates=np.array([0.5, 0.4, 0.6]),
                service_rates=np.array([1.0, 1.2, 0.8]),  # Mean rates
                rewards=np.array([2.0, 3.5, 1.8]),
                customer_types=3,
                resource_types=2,
                time_horizon=2500.0
            ),
            validation_criteria=ValidationCriteria(
                convergence_tolerance=1e-2,
                minimum_episodes=2500,
                statistical_significance_level=0.05,
                confidence_interval_level=0.95,
                effect_size_threshold=0.25,
                validation_level=ValidationLevel.RIGOROUS
            ),
            expected_outcome=ExpectedOutcome(
                optimal_policy_structure="Non-Markovian adaptive policy",
                expected_regret_bound=0.12,
                convergence_episodes_range=(1800, 3000),
                performance_ratio_range=(0.85, 0.95),
                key_insights=[
                    "RL should handle non-Markovian complexity",
                    "Performance bounds established via backcast",
                    "Robustness to distributional assumptions demonstrated"
                ]
            ),
            implementation_notes=[
                "Implement general service time distributions",
                "Include customer abandonment dynamics",
                "Use backcast with perfect service time knowledge",
                "Validate robustness to distributional assumptions"
            ],
            literature_reference="Novel scenario extending beyond Markovian assumptions",
            estimated_runtime_minutes=75
        )
        
        self.scenarios[complex_network.scenario_id] = complex_network
        self.scenarios[non_exponential.scenario_id] = non_exponential
    
    def get_scenario(self, scenario_id: str) -> ExperimentalScenario:
        """Get scenario by ID"""
        if scenario_id not in self.scenarios:
            raise ValueError(f"Scenario {scenario_id} not found")
        return self.scenarios[scenario_id]
    
    def get_scenarios_by_tier(self, tier: ScenarioTier) -> List[ExperimentalScenario]:
        """Get all scenarios for a specific tier"""
        return [scenario for scenario in self.scenarios.values() if scenario.tier == tier]
    
    def validate_scenario_completeness(self) -> Dict[str, Any]:
        """Validate that all scenarios are properly specified"""
        validation_report = {
            'total_scenarios': len(self.scenarios),
            'tier_distribution': {},
            'validation_issues': [],
            'completeness_score': 0.0
        }
        
        # Count scenarios by tier
        for tier in ScenarioTier:
            count = len(self.get_scenarios_by_tier(tier))
            validation_report['tier_distribution'][tier.value] = count
        
        # Check each scenario for completeness
        issues = []
        for scenario_id, scenario in self.scenarios.items():
            # Check required fields
            if not scenario.mathematical_formulation:
                issues.append(f"{scenario_id}: Missing mathematical formulation")
            
            if not scenario.literature_reference:
                issues.append(f"{scenario_id}: Missing literature reference")
            
            if scenario.estimated_runtime_minutes <= 0:
                issues.append(f"{scenario_id}: Invalid runtime estimate")
            
            # Check parameter consistency
            if len(scenario.system_parameters.arrival_rates) != scenario.system_parameters.customer_types:
                issues.append(f"{scenario_id}: Arrival rates don't match customer types")
            
            if len(scenario.system_parameters.rewards) != scenario.system_parameters.customer_types:
                issues.append(f"{scenario_id}: Rewards don't match customer types")
        
        validation_report['validation_issues'] = issues
        validation_report['completeness_score'] = max(0.0, 1.0 - len(issues) / (len(self.scenarios) * 5))
        
        return validation_report
    
    def export_scenarios(self, format_type: str = 'json') -> str:
        """Export scenarios to specified format"""
        if format_type == 'json':
            return self._export_to_json()
        elif format_type == 'yaml':
            return self._export_to_yaml()
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _export_to_json(self) -> str:
        """Export scenarios to JSON format"""
        export_data = {}
        for scenario_id, scenario in self.scenarios.items():
            export_data[scenario_id] = {
                'scenario_id': scenario.scenario_id,
                'tier': scenario.tier.value,
                'name': scenario.name,
                'description': scenario.description,
                'mathematical_formulation': scenario.mathematical_formulation,
                'system_parameters': {
                    'capacity': scenario.system_parameters.capacity.tolist(),
                    'arrival_rates': scenario.system_parameters.arrival_rates.tolist(),
                    'service_rates': scenario.system_parameters.service_rates.tolist(),
                    'rewards': scenario.system_parameters.rewards.tolist(),
                    'customer_types': scenario.system_parameters.customer_types,
                    'resource_types': scenario.system_parameters.resource_types,
                    'time_horizon': scenario.system_parameters.time_horizon,
                    'random_seed': scenario.system_parameters.random_seed
                },
                'validation_criteria': {
                    'convergence_tolerance': scenario.validation_criteria.convergence_tolerance,
                    'minimum_episodes': scenario.validation_criteria.minimum_episodes,
                    'statistical_significance_level': scenario.validation_criteria.statistical_significance_level,
                    'confidence_interval_level': scenario.validation_criteria.confidence_interval_level,
                    'effect_size_threshold': scenario.validation_criteria.effect_size_threshold,
                    'validation_level': scenario.validation_criteria.validation_level.value
                },
                'expected_outcome': {
                    'optimal_policy_structure': scenario.expected_outcome.optimal_policy_structure,
                    'expected_regret_bound': scenario.expected_outcome.expected_regret_bound,
                    'convergence_episodes_range': scenario.expected_outcome.convergence_episodes_range,
                    'performance_ratio_range': scenario.expected_outcome.performance_ratio_range,
                    'key_insights': scenario.expected_outcome.key_insights
                },
                'implementation_notes': scenario.implementation_notes,
                'literature_reference': scenario.literature_reference,
                'estimated_runtime_minutes': scenario.estimated_runtime_minutes
            }
        
        return json.dumps(export_data, indent=2)
    
    def _export_to_yaml(self) -> str:
        """Export scenarios to YAML format"""
        export_data = json.loads(self._export_to_json())
        return yaml.dump(export_data, default_flow_style=False, indent=2)
    
    def generate_execution_plan(self) -> Dict[str, Any]:
        """Generate execution plan for all scenarios"""
        plan = {
            'total_scenarios': len(self.scenarios),
            'estimated_total_runtime_hours': sum(s.estimated_runtime_minutes for s in self.scenarios.values()) / 60,
            'execution_order': [],
            'tier_summary': {}
        }
        
        # Order scenarios by tier and complexity
        tier_order = [ScenarioTier.TIER_1_KNOWN_OPTIMAL, ScenarioTier.TIER_2_CHT_COMPARISON, ScenarioTier.TIER_3_BACKCAST_ANALYSIS]
        
        for tier in tier_order:
            tier_scenarios = self.get_scenarios_by_tier(tier)
            tier_scenarios.sort(key=lambda s: s.estimated_runtime_minutes)
            
            plan['tier_summary'][tier.value] = {
                'scenario_count': len(tier_scenarios),
                'estimated_runtime_hours': sum(s.estimated_runtime_minutes for s in tier_scenarios) / 60,
                'scenarios': [s.scenario_id for s in tier_scenarios]
            }
            
            for scenario in tier_scenarios:
                plan['execution_order'].append({
                    'scenario_id': scenario.scenario_id,
                    'name': scenario.name,
                    'tier': tier.value,
                    'estimated_runtime_minutes': scenario.estimated_runtime_minutes,
                    'validation_level': scenario.validation_criteria.validation_level.value
                })
        
        return plan

# Example usage and validation
if __name__ == "__main__":
    # Create scenario framework
    framework = ScenarioSpecificationFramework()
    
    # Validate completeness
    validation_report = framework.validate_scenario_completeness()
    print("Scenario Validation Report:")
    print(f"Total scenarios: {validation_report['total_scenarios']}")
    print(f"Tier distribution: {validation_report['tier_distribution']}")
    print(f"Completeness score: {validation_report['completeness_score']:.2f}")
    
    if validation_report['validation_issues']:
        print("Validation issues:")
        for issue in validation_report['validation_issues']:
            print(f"  - {issue}")
    else:
        print("✓ All scenarios properly specified")
    
    # Generate execution plan
    execution_plan = framework.generate_execution_plan()
    print(f"\nExecution Plan:")
    print(f"Total runtime: {execution_plan['estimated_total_runtime_hours']:.1f} hours")
    print(f"Scenarios by tier:")
    for tier, summary in execution_plan['tier_summary'].items():
        print(f"  {tier}: {summary['scenario_count']} scenarios, {summary['estimated_runtime_hours']:.1f} hours")
    
    print("\nExperimental scenario specifications completed successfully!")
    print("Ready for implementation and execution.")

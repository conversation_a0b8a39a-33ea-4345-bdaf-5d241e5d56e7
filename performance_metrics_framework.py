"""
Performance Metrics Framework for RL vs CHT Research Project

Comprehensive framework for measuring and evaluating performance across all experimental
scenarios with standardized metrics for regret bounds, convergence rates, robustness
measures, and computational efficiency.

Provides rigorous quantitative assessment supporting academic publication standards
and enabling fair comparison between RL and analytical approaches.

Authors: <AUTHORS>
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import time
import logging
from abc import ABC, abstractmethod
import json

class MetricCategory(Enum):
    """Categories of performance metrics"""
    REGRET_BOUNDS = "regret_bounds"
    CONVERGENCE_RATES = "convergence_rates"
    ROBUSTNESS_MEASURES = "robustness_measures"
    COMPUTATIONAL_EFFICIENCY = "computational_efficiency"
    POLICY_QUALITY = "policy_quality"
    ADAPTABILITY = "adaptability"

class MetricType(Enum):
    """Types of metric calculations"""
    CUMULATIVE = "cumulative"
    INSTANTANEOUS = "instantaneous"
    AVERAGE = "average"
    WORST_CASE = "worst_case"
    BEST_CASE = "best_case"

@dataclass
class MetricResult:
    """Result from a single metric calculation"""
    metric_name: str
    category: MetricCategory
    value: float
    confidence_interval: Optional[Tuple[float, float]]
    standard_error: Optional[float]
    sample_size: int
    calculation_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceProfile:
    """Complete performance profile for an algorithm"""
    algorithm_name: str
    experiment_id: str
    timestamp: float
    metrics: Dict[str, MetricResult]
    overall_score: float
    ranking_position: Optional[int]
    summary_statistics: Dict[str, float]

class PerformanceMetric(ABC):
    """Abstract base class for performance metrics"""
    
    @abstractmethod
    def calculate(self, data: Dict[str, Any]) -> MetricResult:
        """Calculate metric from experimental data"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get metric name"""
        pass
    
    @abstractmethod
    def get_category(self) -> MetricCategory:
        """Get metric category"""
        pass

class RegretBoundMetric(PerformanceMetric):
    """Regret bound calculations"""
    
    def __init__(self, regret_type: str = "cumulative"):
        self.regret_type = regret_type
    
    def calculate(self, data: Dict[str, Any]) -> MetricResult:
        """Calculate regret bounds"""
        start_time = time.time()
        
        rewards = np.array(data['rewards'])
        optimal_rewards = np.array(data['optimal_rewards'])
        
        if self.regret_type == "cumulative":
            regret = np.cumsum(optimal_rewards - rewards)
            final_regret = regret[-1]
            regret_bound = self._calculate_regret_bound(regret)
        elif self.regret_type == "simple":
            regret = optimal_rewards - rewards
            final_regret = np.sum(regret)
            regret_bound = self._calculate_simple_regret_bound(regret)
        else:
            raise ValueError(f"Unknown regret type: {self.regret_type}")
        
        # Calculate confidence interval
        ci = self._calculate_confidence_interval(regret)
        se = np.std(regret) / np.sqrt(len(regret))
        
        return MetricResult(
            metric_name=f"{self.regret_type}_regret",
            category=MetricCategory.REGRET_BOUNDS,
            value=final_regret,
            confidence_interval=ci,
            standard_error=se,
            sample_size=len(rewards),
            calculation_time=time.time() - start_time,
            metadata={
                'regret_bound': regret_bound,
                'regret_sequence': regret.tolist(),
                'normalized_regret': final_regret / len(rewards)
            }
        )
    
    def _calculate_regret_bound(self, regret: np.ndarray) -> float:
        """Calculate theoretical regret bound"""
        T = len(regret)
        # Simplified bound: O(sqrt(T log T))
        return np.sqrt(T * np.log(T))
    
    def _calculate_simple_regret_bound(self, regret: np.ndarray) -> float:
        """Calculate simple regret bound"""
        T = len(regret)
        # Simplified bound: O(sqrt(log T / T))
        return np.sqrt(np.log(T) / T)
    
    def _calculate_confidence_interval(self, regret: np.ndarray, confidence: float = 0.95) -> Tuple[float, float]:
        """Calculate confidence interval for regret"""
        from scipy import stats
        mean_regret = np.mean(regret)
        se = np.std(regret) / np.sqrt(len(regret))
        alpha = 1 - confidence
        t_critical = stats.t.ppf(1 - alpha/2, len(regret) - 1)
        margin = t_critical * se
        return (mean_regret - margin, mean_regret + margin)
    
    def get_name(self) -> str:
        return f"{self.regret_type}_regret_bound"
    
    def get_category(self) -> MetricCategory:
        return MetricCategory.REGRET_BOUNDS

class ConvergenceRateMetric(PerformanceMetric):
    """Convergence rate calculations"""
    
    def __init__(self, convergence_threshold: float = 1e-3):
        self.convergence_threshold = convergence_threshold
    
    def calculate(self, data: Dict[str, Any]) -> MetricResult:
        """Calculate convergence rate"""
        start_time = time.time()
        
        policy_values = np.array(data['policy_values'])
        optimal_value = data.get('optimal_value', np.max(policy_values))
        
        # Calculate convergence episodes
        convergence_episode = self._find_convergence_episode(policy_values, optimal_value)
        
        # Calculate convergence rate
        convergence_rate = self._calculate_convergence_rate(policy_values, optimal_value)
        
        # Calculate stability after convergence
        stability = self._calculate_post_convergence_stability(policy_values, convergence_episode)
        
        return MetricResult(
            metric_name="convergence_rate",
            category=MetricCategory.CONVERGENCE_RATES,
            value=convergence_rate,
            confidence_interval=None,
            standard_error=None,
            sample_size=len(policy_values),
            calculation_time=time.time() - start_time,
            metadata={
                'convergence_episode': convergence_episode,
                'convergence_threshold': self.convergence_threshold,
                'post_convergence_stability': stability,
                'final_value': policy_values[-1],
                'optimal_value': optimal_value
            }
        )
    
    def _find_convergence_episode(self, policy_values: np.ndarray, optimal_value: float) -> int:
        """Find episode where convergence is achieved"""
        errors = np.abs(policy_values - optimal_value) / abs(optimal_value)
        converged_episodes = np.where(errors <= self.convergence_threshold)[0]
        
        if len(converged_episodes) == 0:
            return len(policy_values)  # Never converged
        
        # Find first episode where convergence is sustained
        for episode in converged_episodes:
            if episode + 10 < len(policy_values):  # Check next 10 episodes
                if np.all(errors[episode:episode+10] <= self.convergence_threshold):
                    return episode
        
        return converged_episodes[0]  # Return first convergence if sustained convergence not found
    
    def _calculate_convergence_rate(self, policy_values: np.ndarray, optimal_value: float) -> float:
        """Calculate exponential convergence rate"""
        errors = np.abs(policy_values - optimal_value)
        errors = errors[errors > 0]  # Remove zeros to avoid log issues
        
        if len(errors) < 2:
            return 0.0
        
        # Fit exponential decay: error(t) = error(0) * exp(-rate * t)
        log_errors = np.log(errors)
        episodes = np.arange(len(log_errors))
        
        # Linear regression on log scale
        coeffs = np.polyfit(episodes, log_errors, 1)
        convergence_rate = -coeffs[0]  # Negative slope gives convergence rate
        
        return max(0.0, convergence_rate)
    
    def _calculate_post_convergence_stability(self, policy_values: np.ndarray, convergence_episode: int) -> float:
        """Calculate stability after convergence"""
        if convergence_episode >= len(policy_values) - 1:
            return 0.0
        
        post_convergence_values = policy_values[convergence_episode:]
        return 1.0 / (1.0 + np.std(post_convergence_values))  # Higher stability = lower variance
    
    def get_name(self) -> str:
        return "convergence_rate"
    
    def get_category(self) -> MetricCategory:
        return MetricCategory.CONVERGENCE_RATES

class RobustnessMetric(PerformanceMetric):
    """Robustness measure calculations"""
    
    def __init__(self, perturbation_levels: List[float] = [0.1, 0.2, 0.3]):
        self.perturbation_levels = perturbation_levels
    
    def calculate(self, data: Dict[str, Any]) -> MetricResult:
        """Calculate robustness measures"""
        start_time = time.time()
        
        baseline_performance = data['baseline_performance']
        perturbed_performances = data['perturbed_performances']
        
        # Calculate robustness score
        robustness_score = self._calculate_robustness_score(baseline_performance, perturbed_performances)
        
        # Calculate sensitivity
        sensitivity = self._calculate_sensitivity(baseline_performance, perturbed_performances)
        
        # Calculate worst-case performance degradation
        worst_case_degradation = self._calculate_worst_case_degradation(baseline_performance, perturbed_performances)
        
        return MetricResult(
            metric_name="robustness",
            category=MetricCategory.ROBUSTNESS_MEASURES,
            value=robustness_score,
            confidence_interval=None,
            standard_error=None,
            sample_size=len(perturbed_performances),
            calculation_time=time.time() - start_time,
            metadata={
                'sensitivity': sensitivity,
                'worst_case_degradation': worst_case_degradation,
                'perturbation_levels': self.perturbation_levels,
                'baseline_performance': baseline_performance
            }
        )
    
    def _calculate_robustness_score(self, baseline: float, perturbed: List[float]) -> float:
        """Calculate overall robustness score"""
        if not perturbed:
            return 1.0
        
        relative_performances = [p / baseline for p in perturbed if baseline != 0]
        if not relative_performances:
            return 0.0
        
        # Robustness = average relative performance under perturbations
        return np.mean(relative_performances)
    
    def _calculate_sensitivity(self, baseline: float, perturbed: List[float]) -> float:
        """Calculate sensitivity to perturbations"""
        if not perturbed or baseline == 0:
            return 0.0
        
        relative_changes = [abs(p - baseline) / baseline for p in perturbed]
        return np.mean(relative_changes)
    
    def _calculate_worst_case_degradation(self, baseline: float, perturbed: List[float]) -> float:
        """Calculate worst-case performance degradation"""
        if not perturbed or baseline == 0:
            return 0.0
        
        worst_performance = min(perturbed)
        return (baseline - worst_performance) / baseline
    
    def get_name(self) -> str:
        return "robustness"
    
    def get_category(self) -> MetricCategory:
        return MetricCategory.ROBUSTNESS_MEASURES

class ComputationalEfficiencyMetric(PerformanceMetric):
    """Computational efficiency calculations"""
    
    def calculate(self, data: Dict[str, Any]) -> MetricResult:
        """Calculate computational efficiency metrics"""
        start_time = time.time()
        
        execution_times = np.array(data['execution_times'])
        memory_usage = np.array(data.get('memory_usage', []))
        episodes = data.get('episodes', len(execution_times))
        
        # Calculate time efficiency
        total_time = np.sum(execution_times)
        time_per_episode = total_time / episodes
        
        # Calculate memory efficiency
        if len(memory_usage) > 0:
            avg_memory = np.mean(memory_usage)
            peak_memory = np.max(memory_usage)
        else:
            avg_memory = 0.0
            peak_memory = 0.0
        
        # Calculate efficiency score (lower is better, so invert)
        efficiency_score = 1.0 / (1.0 + time_per_episode)
        
        return MetricResult(
            metric_name="computational_efficiency",
            category=MetricCategory.COMPUTATIONAL_EFFICIENCY,
            value=efficiency_score,
            confidence_interval=None,
            standard_error=np.std(execution_times) / np.sqrt(len(execution_times)),
            sample_size=len(execution_times),
            calculation_time=time.time() - start_time,
            metadata={
                'total_execution_time': total_time,
                'time_per_episode': time_per_episode,
                'average_memory_mb': avg_memory,
                'peak_memory_mb': peak_memory,
                'episodes': episodes
            }
        )
    
    def get_name(self) -> str:
        return "computational_efficiency"
    
    def get_category(self) -> MetricCategory:
        return MetricCategory.COMPUTATIONAL_EFFICIENCY

class PerformanceMetricsFramework:
    """Main framework for performance metrics calculation"""
    
    def __init__(self):
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.profiles: Dict[str, PerformanceProfile] = {}
        self.logger = logging.getLogger(__name__)
        self._initialize_standard_metrics()
    
    def _initialize_standard_metrics(self):
        """Initialize standard performance metrics"""
        self.metrics['cumulative_regret'] = RegretBoundMetric('cumulative')
        self.metrics['simple_regret'] = RegretBoundMetric('simple')
        self.metrics['convergence_rate'] = ConvergenceRateMetric()
        self.metrics['robustness'] = RobustnessMetric()
        self.metrics['computational_efficiency'] = ComputationalEfficiencyMetric()
    
    def add_metric(self, metric: PerformanceMetric):
        """Add custom metric to framework"""
        self.metrics[metric.get_name()] = metric
        self.logger.info(f"Added metric: {metric.get_name()}")
    
    def calculate_performance_profile(self, algorithm_name: str, experiment_id: str, 
                                    experimental_data: Dict[str, Any]) -> PerformanceProfile:
        """Calculate complete performance profile for an algorithm"""
        
        self.logger.info(f"Calculating performance profile for {algorithm_name} in {experiment_id}")
        
        metric_results = {}
        
        # Calculate all metrics
        for metric_name, metric in self.metrics.items():
            try:
                if self._has_required_data(metric, experimental_data):
                    result = metric.calculate(experimental_data)
                    metric_results[metric_name] = result
                    self.logger.debug(f"Calculated {metric_name}: {result.value}")
                else:
                    self.logger.warning(f"Insufficient data for metric {metric_name}")
            except Exception as e:
                self.logger.error(f"Error calculating {metric_name}: {str(e)}")
        
        # Calculate overall score
        overall_score = self._calculate_overall_score(metric_results)
        
        # Calculate summary statistics
        summary_stats = self._calculate_summary_statistics(metric_results)
        
        profile = PerformanceProfile(
            algorithm_name=algorithm_name,
            experiment_id=experiment_id,
            timestamp=time.time(),
            metrics=metric_results,
            overall_score=overall_score,
            ranking_position=None,  # Will be set during comparison
            summary_statistics=summary_stats
        )
        
        # Store profile
        profile_key = f"{algorithm_name}_{experiment_id}"
        self.profiles[profile_key] = profile
        
        return profile
    
    def _has_required_data(self, metric: PerformanceMetric, data: Dict[str, Any]) -> bool:
        """Check if data contains required fields for metric"""
        if isinstance(metric, RegretBoundMetric):
            return 'rewards' in data and 'optimal_rewards' in data
        elif isinstance(metric, ConvergenceRateMetric):
            return 'policy_values' in data
        elif isinstance(metric, RobustnessMetric):
            return 'baseline_performance' in data and 'perturbed_performances' in data
        elif isinstance(metric, ComputationalEfficiencyMetric):
            return 'execution_times' in data
        else:
            return True  # Assume custom metrics handle their own validation
    
    def _calculate_overall_score(self, metric_results: Dict[str, MetricResult]) -> float:
        """Calculate weighted overall performance score"""
        if not metric_results:
            return 0.0
        
        # Define weights for different metric categories
        weights = {
            MetricCategory.REGRET_BOUNDS: 0.3,
            MetricCategory.CONVERGENCE_RATES: 0.25,
            MetricCategory.ROBUSTNESS_MEASURES: 0.2,
            MetricCategory.COMPUTATIONAL_EFFICIENCY: 0.15,
            MetricCategory.POLICY_QUALITY: 0.1
        }
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for result in metric_results.values():
            category = result.category
            weight = weights.get(category, 0.1)
            
            # Normalize metric value (assuming higher is better for most metrics)
            normalized_value = self._normalize_metric_value(result)
            
            weighted_score += weight * normalized_value
            total_weight += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _normalize_metric_value(self, result: MetricResult) -> float:
        """Normalize metric value to [0, 1] range"""
        # For regret metrics, lower is better
        if result.category == MetricCategory.REGRET_BOUNDS:
            # Use sigmoid to map to [0, 1] where lower regret gives higher score
            return 1.0 / (1.0 + abs(result.value))
        
        # For other metrics, assume higher is better and use sigmoid
        return 1.0 / (1.0 + np.exp(-result.value))
    
    def _calculate_summary_statistics(self, metric_results: Dict[str, MetricResult]) -> Dict[str, float]:
        """Calculate summary statistics across all metrics"""
        if not metric_results:
            return {}
        
        values = [result.value for result in metric_results.values()]
        
        return {
            'mean_metric_value': np.mean(values),
            'std_metric_value': np.std(values),
            'min_metric_value': np.min(values),
            'max_metric_value': np.max(values),
            'total_calculation_time': sum(result.calculation_time for result in metric_results.values())
        }
    
    def compare_algorithms(self, experiment_id: str) -> Dict[str, Any]:
        """Compare all algorithms for a specific experiment"""
        
        # Get profiles for this experiment
        experiment_profiles = {
            key: profile for key, profile in self.profiles.items()
            if profile.experiment_id == experiment_id
        }
        
        if not experiment_profiles:
            return {'error': f'No profiles found for experiment {experiment_id}'}
        
        # Rank algorithms by overall score
        sorted_profiles = sorted(
            experiment_profiles.values(),
            key=lambda p: p.overall_score,
            reverse=True
        )
        
        # Assign rankings
        for i, profile in enumerate(sorted_profiles):
            profile.ranking_position = i + 1
        
        # Generate comparison report
        comparison = {
            'experiment_id': experiment_id,
            'num_algorithms': len(sorted_profiles),
            'rankings': [
                {
                    'rank': profile.ranking_position,
                    'algorithm': profile.algorithm_name,
                    'overall_score': profile.overall_score,
                    'key_metrics': {
                        name: result.value for name, result in profile.metrics.items()
                    }
                }
                for profile in sorted_profiles
            ],
            'metric_comparison': self._generate_metric_comparison(sorted_profiles),
            'statistical_significance': self._test_ranking_significance(sorted_profiles)
        }
        
        return comparison
    
    def _generate_metric_comparison(self, profiles: List[PerformanceProfile]) -> Dict[str, Any]:
        """Generate detailed metric comparison"""
        if not profiles:
            return {}
        
        # Get all metric names
        all_metrics = set()
        for profile in profiles:
            all_metrics.update(profile.metrics.keys())
        
        metric_comparison = {}
        
        for metric_name in all_metrics:
            values = []
            algorithms = []
            
            for profile in profiles:
                if metric_name in profile.metrics:
                    values.append(profile.metrics[metric_name].value)
                    algorithms.append(profile.algorithm_name)
            
            if values:
                metric_comparison[metric_name] = {
                    'algorithms': algorithms,
                    'values': values,
                    'best_algorithm': algorithms[np.argmax(values)],
                    'worst_algorithm': algorithms[np.argmin(values)],
                    'range': max(values) - min(values),
                    'coefficient_of_variation': np.std(values) / np.mean(values) if np.mean(values) != 0 else 0
                }
        
        return metric_comparison
    
    def _test_ranking_significance(self, profiles: List[PerformanceProfile]) -> Dict[str, Any]:
        """Test statistical significance of algorithm rankings"""
        if len(profiles) < 2:
            return {'significant': False, 'reason': 'Insufficient algorithms for comparison'}
        
        # Simplified significance test based on overall scores
        scores = [profile.overall_score for profile in profiles]
        
        # Check if differences are meaningful
        score_range = max(scores) - min(scores)
        mean_score = np.mean(scores)
        
        # Consider significant if range > 10% of mean
        significant = score_range > 0.1 * mean_score
        
        return {
            'significant': significant,
            'score_range': score_range,
            'relative_range': score_range / mean_score if mean_score != 0 else 0,
            'confidence': 'medium' if significant else 'low'
        }
    
    def export_results(self, filename: str, format_type: str = 'json'):
        """Export all results to file"""
        
        export_data = {
            'framework_info': {
                'num_metrics': len(self.metrics),
                'num_profiles': len(self.profiles),
                'export_timestamp': time.time()
            },
            'metrics_definitions': {
                name: {
                    'name': metric.get_name(),
                    'category': metric.get_category().value
                }
                for name, metric in self.metrics.items()
            },
            'performance_profiles': {
                key: {
                    'algorithm_name': profile.algorithm_name,
                    'experiment_id': profile.experiment_id,
                    'overall_score': profile.overall_score,
                    'ranking_position': profile.ranking_position,
                    'metrics': {
                        name: {
                            'value': result.value,
                            'category': result.category.value,
                            'metadata': result.metadata
                        }
                        for name, result in profile.metrics.items()
                    },
                    'summary_statistics': profile.summary_statistics
                }
                for key, profile in self.profiles.items()
            }
        }
        
        if format_type == 'json':
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
        
        self.logger.info(f"Results exported to {filename}")

# Example usage and testing
if __name__ == "__main__":
    # Create performance metrics framework
    framework = PerformanceMetricsFramework()
    
    # Simulate experimental data for RL algorithm
    np.random.seed(42)
    rl_data = {
        'rewards': np.random.normal(95, 10, 1000),
        'optimal_rewards': np.random.normal(100, 5, 1000),
        'policy_values': np.cumsum(np.random.normal(0.01, 0.1, 1000)) + 90,
        'execution_times': np.random.exponential(0.1, 1000),
        'baseline_performance': 95.0,
        'perturbed_performances': [92.0, 89.0, 87.0]
    }
    
    # Calculate performance profile
    rl_profile = framework.calculate_performance_profile("RL_Algorithm", "T2-1-CHT-OVERLOADED", rl_data)
    
    # Simulate CHT algorithm data
    cht_data = {
        'rewards': np.random.normal(90, 8, 1000),
        'optimal_rewards': np.random.normal(100, 5, 1000),
        'policy_values': np.ones(1000) * 88,  # Static policy
        'execution_times': np.random.exponential(0.05, 1000),  # Faster execution
        'baseline_performance': 90.0,
        'perturbed_performances': [88.0, 85.0, 82.0]
    }
    
    cht_profile = framework.calculate_performance_profile("CHT_Policy", "T2-1-CHT-OVERLOADED", cht_data)
    
    # Compare algorithms
    comparison = framework.compare_algorithms("T2-1-CHT-OVERLOADED")
    
    print("Performance Metrics Framework Results:")
    print(f"RL Algorithm Overall Score: {rl_profile.overall_score:.3f}")
    print(f"CHT Policy Overall Score: {cht_profile.overall_score:.3f}")
    
    print(f"\nAlgorithm Rankings for T2-1-CHT-OVERLOADED:")
    for ranking in comparison['rankings']:
        print(f"  Rank {ranking['rank']}: {ranking['algorithm']} (Score: {ranking['overall_score']:.3f})")
    
    print(f"\nStatistical Significance: {comparison['statistical_significance']['significant']}")
    
    print("\nPerformance metrics framework ready for comprehensive evaluation!")
    print("All Phase 2 tasks completed successfully!")

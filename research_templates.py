"""
Research Documentation Templates
RL vs CHT Research Project

Templates for consistent documentation across all research activities.

Authors: <AUTHORS>
"""

from typing import Dict, List, Any
import datetime

class DocumentationTemplates:
    """Collection of templates for research documentation"""
    
    @staticmethod
    def daily_progress_template() -> Dict[str, Any]:
        """Template for daily progress reports"""
        return {
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "current_tasks": [],
            "completed_today": [],
            "challenges_encountered": [],
            "solutions_implemented": [],
            "insights_gained": [],
            "tomorrow_plan": [],
            "time_spent": {
                "research": 0,
                "implementation": 0,
                "writing": 0,
                "meetings": 0,
                "other": 0
            },
            "resources_used": [],
            "notes": ""
        }
    
    @staticmethod
    def experiment_template() -> Dict[str, Any]:
        """Template for experiment documentation"""
        return {
            "experiment_id": "",
            "title": "",
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "objective": "",
            "hypothesis": "",
            "methodology": {
                "algorithm": "",
                "parameters": {},
                "data_sources": [],
                "evaluation_metrics": [],
                "baseline_comparisons": []
            },
            "setup": {
                "hardware": "",
                "software": "",
                "environment": {},
                "random_seeds": [],
                "validation_method": ""
            },
            "execution": {
                "start_time": "",
                "end_time": "",
                "duration": "",
                "status": "planned",  # planned, running, completed, failed
                "progress_notes": []
            },
            "results": {
                "primary_metrics": {},
                "secondary_metrics": {},
                "statistical_significance": {},
                "confidence_intervals": {},
                "visualizations": [],
                "raw_data_location": ""
            },
            "analysis": {
                "findings": [],
                "interpretation": "",
                "limitations": [],
                "unexpected_results": [],
                "implications": []
            },
            "validation": {
                "reproducibility_check": False,
                "cross_validation": False,
                "independent_verification": False,
                "validation_notes": ""
            },
            "next_steps": [],
            "related_experiments": [],
            "attachments": []
        }
    
    @staticmethod
    def decision_template() -> Dict[str, Any]:
        """Template for decision documentation"""
        return {
            "decision_id": "",
            "title": "",
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "decision_maker": "",
            "stakeholders": [],
            "context": {
                "background": "",
                "problem_statement": "",
                "constraints": [],
                "requirements": []
            },
            "alternatives": [
                {
                    "option": "",
                    "description": "",
                    "pros": [],
                    "cons": [],
                    "risks": [],
                    "cost": "",
                    "timeline": ""
                }
            ],
            "evaluation_criteria": [],
            "analysis": {
                "method": "",
                "data_sources": [],
                "assumptions": [],
                "sensitivity_analysis": ""
            },
            "decision": {
                "chosen_option": "",
                "rationale": "",
                "confidence_level": "",
                "dissenting_opinions": []
            },
            "implementation": {
                "action_items": [],
                "timeline": "",
                "responsible_parties": [],
                "success_metrics": [],
                "monitoring_plan": ""
            },
            "review": {
                "review_date": "",
                "outcome": "",
                "lessons_learned": [],
                "would_decide_differently": ""
            }
        }
    
    @staticmethod
    def checkpoint_template() -> Dict[str, Any]:
        """Template for phase checkpoint reviews"""
        return {
            "checkpoint_id": "",
            "phase": "",
            "date": datetime.date.today().isoformat(),
            "reviewers": [],
            "objectives": {
                "planned_objectives": [],
                "completed_objectives": [],
                "partially_completed": [],
                "not_started": [],
                "added_objectives": []
            },
            "deliverables": {
                "planned_deliverables": [],
                "completed_deliverables": [],
                "in_progress": [],
                "delayed": [],
                "quality_assessment": {}
            },
            "timeline": {
                "planned_duration": "",
                "actual_duration": "",
                "variance": "",
                "reasons_for_variance": []
            },
            "resources": {
                "planned_resources": {},
                "actual_resources": {},
                "resource_efficiency": "",
                "additional_resources_needed": []
            },
            "quality_metrics": {
                "validation_results": {},
                "test_coverage": "",
                "documentation_completeness": "",
                "code_quality": "",
                "reproducibility": ""
            },
            "risks_and_issues": {
                "identified_risks": [],
                "materialized_risks": [],
                "mitigation_effectiveness": [],
                "new_risks": [],
                "critical_issues": []
            },
            "learnings": {
                "key_insights": [],
                "methodology_improvements": [],
                "tool_effectiveness": [],
                "process_improvements": [],
                "knowledge_gaps": []
            },
            "next_phase": {
                "readiness_assessment": "",
                "prerequisites_met": [],
                "outstanding_dependencies": [],
                "recommended_adjustments": [],
                "go_no_go_decision": ""
            },
            "overall_assessment": {
                "phase_success_rating": "",
                "confidence_in_results": "",
                "stakeholder_satisfaction": "",
                "recommendations": []
            }
        }
    
    @staticmethod
    def literature_review_template() -> Dict[str, Any]:
        """Template for literature review entries"""
        return {
            "entry_id": "",
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "paper_details": {
                "title": "",
                "authors": [],
                "journal": "",
                "year": "",
                "doi": "",
                "url": "",
                "citation": ""
            },
            "relevance": {
                "domain": "",  # OR, RL, RL-OR intersection
                "relevance_score": "",  # 1-5 scale
                "key_topics": [],
                "research_questions_addressed": []
            },
            "content_summary": {
                "abstract_summary": "",
                "methodology": "",
                "key_findings": [],
                "contributions": [],
                "limitations": []
            },
            "analysis": {
                "strengths": [],
                "weaknesses": [],
                "novelty": "",
                "significance": "",
                "reproducibility": ""
            },
            "connections": {
                "related_papers": [],
                "builds_on": [],
                "contradicts": [],
                "supports_our_thesis": "",
                "gaps_identified": []
            },
            "implications": {
                "for_our_research": "",
                "methodology_insights": [],
                "theoretical_implications": [],
                "practical_applications": []
            },
            "notes": "",
            "follow_up_actions": [],
            "tags": []
        }
    
    @staticmethod
    def meeting_template() -> Dict[str, Any]:
        """Template for meeting documentation"""
        return {
            "meeting_id": "",
            "date": datetime.date.today().isoformat(),
            "time": "",
            "duration": "",
            "type": "",  # advisor, team, external, conference
            "participants": [],
            "agenda": [],
            "discussion_points": [
                {
                    "topic": "",
                    "discussion": "",
                    "decisions": [],
                    "action_items": []
                }
            ],
            "decisions_made": [],
            "action_items": [
                {
                    "item": "",
                    "responsible": "",
                    "deadline": "",
                    "priority": "",
                    "status": "open"
                }
            ],
            "feedback_received": [],
            "concerns_raised": [],
            "next_meeting": {
                "date": "",
                "agenda_items": [],
                "preparation_required": []
            },
            "attachments": [],
            "notes": ""
        }
    
    @staticmethod
    def problem_solution_template() -> Dict[str, Any]:
        """Template for problem and solution documentation"""
        return {
            "entry_id": "",
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "problem": {
                "title": "",
                "description": "",
                "category": "",  # technical, methodological, resource, timeline
                "severity": "",  # low, medium, high, critical
                "impact": "",
                "root_cause": "",
                "symptoms": []
            },
            "investigation": {
                "methods_used": [],
                "data_collected": [],
                "hypotheses_tested": [],
                "findings": []
            },
            "solutions": [
                {
                    "solution": "",
                    "description": "",
                    "pros": [],
                    "cons": [],
                    "effort_required": "",
                    "timeline": "",
                    "risks": []
                }
            ],
            "implementation": {
                "chosen_solution": "",
                "rationale": "",
                "implementation_steps": [],
                "timeline": "",
                "resources_needed": [],
                "success_criteria": []
            },
            "outcome": {
                "status": "",  # resolved, partially_resolved, ongoing
                "effectiveness": "",
                "side_effects": [],
                "lessons_learned": [],
                "prevention_measures": []
            },
            "related_issues": [],
            "follow_up": []
        }
    
    @staticmethod
    def milestone_template() -> Dict[str, Any]:
        """Template for milestone documentation"""
        return {
            "milestone_id": "",
            "title": "",
            "date": datetime.date.today().isoformat(),
            "phase": "",
            "description": "",
            "significance": "",
            "criteria": {
                "success_criteria": [],
                "acceptance_criteria": [],
                "quality_standards": []
            },
            "achievement": {
                "status": "",  # achieved, partially_achieved, not_achieved
                "evidence": [],
                "validation": [],
                "stakeholder_approval": []
            },
            "impact": {
                "on_timeline": "",
                "on_budget": "",
                "on_scope": "",
                "on_quality": "",
                "on_risk": ""
            },
            "celebration": {
                "recognition": [],
                "team_acknowledgment": "",
                "stakeholder_communication": []
            },
            "next_milestones": [],
            "lessons_learned": []
        }

# Utility functions for template usage
def create_template(template_type: str) -> Dict[str, Any]:
    """Create a template of the specified type"""
    templates = DocumentationTemplates()
    
    template_map = {
        "daily_progress": templates.daily_progress_template,
        "experiment": templates.experiment_template,
        "decision": templates.decision_template,
        "checkpoint": templates.checkpoint_template,
        "literature": templates.literature_review_template,
        "meeting": templates.meeting_template,
        "problem_solution": templates.problem_solution_template,
        "milestone": templates.milestone_template
    }
    
    if template_type not in template_map:
        raise ValueError(f"Unknown template type: {template_type}")
    
    return template_map[template_type]()

def get_available_templates() -> List[str]:
    """Get list of available template types"""
    return [
        "daily_progress",
        "experiment", 
        "decision",
        "checkpoint",
        "literature",
        "meeting",
        "problem_solution",
        "milestone"
    ]

if __name__ == "__main__":
    # Example usage
    print("Available templates:")
    for template_type in get_available_templates():
        print(f"  - {template_type}")
    
    # Create an example experiment template
    exp_template = create_template("experiment")
    print(f"\nExperiment template has {len(exp_template)} fields")
    print("Key sections:", list(exp_template.keys()))

"""
Tier 2 CHT Comparison Experiments for RL vs CHT Research Project

Execute comprehensive experiments comparing RL against CHT policy in:
- Overloaded network scenarios
- Underloaded network scenarios
- Various network topologies and configurations

These experiments validate RL performance against state-of-the-art analytical methods.

Authors: <AUTHORS>
"""

import numpy as np
import json
import time
from datetime import datetime
from pathlib import Path

from core_rl_algorithms import RLAlgorithmFactory, R<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>
from simulation_environment import EnvironmentFactory, ScenarioType
from analytical_benchmarks import AnalyticalBenchmarkFramework, PolicyParameters

def run_tier2_cht_experiments():
    """Run comprehensive Tier 2 CHT comparison experiments"""
    
    print("🚀 Starting Tier 2 CHT Comparison Experiments")
    print("=" * 60)
    
    results_dir = Path("tier2_results")
    results_dir.mkdir(parents=True, exist_ok=True)
    
    # Tier 2 experiment configurations
    experiments = [
        {
            'name': 'CHT_Overloaded_Network',
            'description': 'Overloaded network scenario where ρ > 1.2',
            'scenario': ScenarioType.XIE_2024_CHT,
            'params': {
                'resource_capacities': [8, 6, 10],
                'customer_types': 4,
                'arrival_rates': [0.8, 0.6, 0.7, 0.5],  # High arrival rates
                'service_rates': [1.0, 1.2, 0.8, 1.1],
                'rewards': [2.0, 3.0, 1.5, 2.5],
                'time_horizon': 200.0
            },
            'training_episodes': 300,
            'evaluation_episodes': 50,
            'expected_rl_advantage': 0.05,  # RL should be 5%+ better than CHT
            'load_factor': 'overloaded'
        },
        {
            'name': 'CHT_Underloaded_Network',
            'description': 'Underloaded network scenario where ρ < 0.8',
            'scenario': ScenarioType.XIE_2024_CHT,
            'params': {
                'resource_capacities': [12, 10, 15],  # Higher capacity
                'customer_types': 4,
                'arrival_rates': [0.3, 0.2, 0.25, 0.15],  # Lower arrival rates
                'service_rates': [1.0, 1.2, 0.8, 1.1],
                'rewards': [2.0, 3.0, 1.5, 2.5],
                'time_horizon': 200.0
            },
            'training_episodes': 300,
            'evaluation_episodes': 50,
            'expected_rl_advantage': 0.10,  # RL should be 10%+ better in underloaded
            'load_factor': 'underloaded'
        },
        {
            'name': 'CHT_Balanced_Network',
            'description': 'Balanced network scenario where ρ ≈ 1.0',
            'scenario': ScenarioType.XIE_2024_CHT,
            'params': {
                'resource_capacities': [10, 8, 12],
                'customer_types': 3,
                'arrival_rates': [0.5, 0.4, 0.3],  # Balanced rates
                'service_rates': [1.0, 1.0, 1.0],
                'rewards': [2.0, 3.0, 1.5],
                'time_horizon': 200.0
            },
            'training_episodes': 300,
            'evaluation_episodes': 50,
            'expected_rl_advantage': 0.02,  # Smaller advantage in balanced case
            'load_factor': 'balanced'
        },
        {
            'name': 'CHT_High_Variability',
            'description': 'High variability in service rates and rewards',
            'scenario': ScenarioType.XIE_2024_CHT,
            'params': {
                'resource_capacities': [6, 8, 5],
                'customer_types': 3,
                'arrival_rates': [0.4, 0.3, 0.2],
                'service_rates': [0.5, 2.0, 1.5],  # High variability
                'rewards': [1.0, 5.0, 2.0],  # High variability
                'time_horizon': 200.0
            },
            'training_episodes': 400,  # More training for complex scenario
            'evaluation_episodes': 50,
            'expected_rl_advantage': 0.15,  # RL should excel with variability
            'load_factor': 'variable'
        }
    ]
    
    results = {}
    
    for exp in experiments:
        print(f"\n📊 Running experiment: {exp['name']}")
        print(f"   Description: {exp['description']}")
        
        try:
            # Calculate load factor
            load_factor = calculate_load_factor(exp['params'])
            print(f"   Load factor: {load_factor:.3f}")
            
            # Run RL experiment
            print("   🤖 Training RL agent...")
            rl_result = run_rl_experiment(exp)
            
            # Run CHT benchmark
            print("   📈 Running CHT benchmark...")
            cht_result = run_cht_benchmark(exp)
            
            # Compare results
            comparison = compare_rl_vs_cht(rl_result, cht_result, exp)
            
            results[exp['name']] = {
                'config': exp,
                'load_factor': load_factor,
                'rl_performance': rl_result['final_performance'],
                'cht_performance': cht_result['performance'],
                'performance_ratio': comparison['performance_ratio'],
                'rl_advantage': comparison['rl_advantage'],
                'meets_expectation': comparison['meets_expectation'],
                'statistical_significance': comparison['statistical_significance'],
                'success': comparison['success']
            }
            
            print(f"   ✓ RL Performance: {rl_result['final_performance']:.2f}")
            print(f"   ✓ CHT Performance: {cht_result['performance']:.2f}")
            print(f"   ✓ RL Advantage: {comparison['rl_advantage']:+.1%}")
            print(f"   ✓ Success: {comparison['success']}")
            
        except Exception as e:
            print(f"   ❌ Experiment {exp['name']} failed: {str(e)}")
            results[exp['name']] = {
                'config': exp,
                'success': False,
                'error': str(e)
            }
    
    # Generate comprehensive report
    report = generate_tier2_report(results)
    
    # Save results
    with open(results_dir / "tier2_detailed_results.json", 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    with open(results_dir / "tier2_summary_report.json", 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    # Display summary
    print("\n" + "=" * 60)
    print("🎯 TIER 2 CHT COMPARISON SUMMARY")
    print("=" * 60)
    
    print(f"Total Experiments: {report['total_experiments']}")
    print(f"Successful Experiments: {report['successful_experiments']}")
    print(f"Success Rate: {report['success_rate']:.1%}")
    print(f"Average RL Advantage: {report['average_rl_advantage']:+.1%}")
    print(f"Experiments Where RL > CHT: {report['rl_superior_count']}")
    
    print(f"\n🔬 VALIDATION EVIDENCE:")
    evidence = report['validation_evidence']
    print(f"✓ RL Outperforms CHT: {evidence['rl_outperforms_cht']}")
    print(f"✓ Consistent Across Scenarios: {evidence['consistent_across_scenarios']}")
    print(f"✓ Adapts to Load Conditions: {evidence['adapts_to_load_conditions']}")
    print(f"✓ Supports Paradigm Shift: {evidence['supports_paradigm_shift']}")
    
    if evidence['supports_paradigm_shift']:
        print("\n🎉 TIER 2 VALIDATION SUCCESSFUL!")
        print("RL demonstrates superiority over state-of-the-art CHT policy.")
        print("Strong evidence for paradigm shift in network resource allocation.")
    else:
        print("\n⚠️  TIER 2 VALIDATION MIXED RESULTS")
        print("Some scenarios show RL advantages, others need improvement.")
    
    return report

def calculate_load_factor(params):
    """Calculate network load factor (ρ)"""
    arrival_rates = np.array(params['arrival_rates'])
    service_rates = np.array(params['service_rates'])
    
    # Simplified load calculation
    total_arrival_rate = np.sum(arrival_rates)
    average_service_rate = np.mean(service_rates)
    
    return total_arrival_rate / average_service_rate

def run_rl_experiment(exp_config):
    """Run RL experiment for CHT comparison"""
    
    # Create environment
    env = EnvironmentFactory.create_environment(
        exp_config['scenario'], **exp_config['params']
    )
    
    # Create RL agent
    state = env.reset()
    state_dim = len(state)
    action_dim = env.get_action_dim()
    
    # Use PPO for network scenarios (better for continuous-like problems)
    rl_config = RLConfig(
        learning_rate=3e-4,
        batch_size=32,
        gamma=0.99,
        epsilon_start=1.0,
        epsilon_end=0.05,
        epsilon_decay=0.995
    )
    
    agent = RLAlgorithmFactory.create_algorithm('ppo', state_dim, action_dim, rl_config)
    trainer = RLTrainer(agent, env)
    
    # Train agent
    training_results = trainer.train(
        num_episodes=exp_config['training_episodes'],
        max_steps_per_episode=300
    )
    
    # Evaluate agent
    evaluation_results = trainer.evaluate(num_episodes=exp_config['evaluation_episodes'])
    
    return {
        'training_results': training_results,
        'evaluation_results': evaluation_results,
        'final_performance': evaluation_results['mean_reward'],
        'performance_std': evaluation_results['std_reward'],
        'convergence_achieved': len(training_results['episode_rewards']) > 100
    }

def run_cht_benchmark(exp_config):
    """Run CHT policy benchmark"""
    
    framework = AnalyticalBenchmarkFramework()
    
    # Create CHT policy
    params = PolicyParameters(
        capacity=np.array(exp_config['params']['resource_capacities']),
        arrival_rates=np.array(exp_config['params']['arrival_rates']),
        service_rates=np.array(exp_config['params']['service_rates']),
        rewards=np.array(exp_config['params']['rewards']),
        customer_types=exp_config['params']['customer_types'],
        resource_types=len(exp_config['params']['resource_capacities']),
        additional_params={
            'resource_requirements': np.random.randint(0, 2, 
                size=(exp_config['params']['customer_types'], 
                     len(exp_config['params']['resource_capacities'])))
        }
    )
    
    policy = framework.create_cht_policy(params)
    
    # Simulate CHT policy performance
    env = EnvironmentFactory.create_environment(
        exp_config['scenario'], **exp_config['params']
    )
    
    cht_rewards = []
    
    for episode in range(exp_config['evaluation_episodes']):
        state = env.reset()
        episode_reward = 0.0
        steps = 0
        
        while steps < 300:  # Limit steps
            if hasattr(env, 'pending_customer') and env.pending_customer:
                policy_state = {
                    'resource_occupancy': getattr(env, 'resource_occupancy', np.zeros(3)),
                    'active_customers': getattr(env, 'active_customers', {})
                }
                customer_info = {'customer_type': env.pending_customer.customer_type}
                action = policy.get_action(policy_state, customer_info)
            else:
                action = 0
            
            next_state, reward, done, info = env.step(action)
            episode_reward += reward
            steps += 1
            
            if done:
                break
            
            state = next_state
        
        cht_rewards.append(episode_reward)
    
    return {
        'policy': policy,
        'performance': np.mean(cht_rewards),
        'performance_std': np.std(cht_rewards),
        'all_rewards': cht_rewards,
        'thresholds': policy.cht_thresholds
    }

def compare_rl_vs_cht(rl_result, cht_result, exp_config):
    """Compare RL vs CHT performance"""
    
    rl_performance = rl_result['final_performance']
    cht_performance = cht_result['performance']
    
    if cht_performance > 0:
        performance_ratio = rl_performance / cht_performance
        rl_advantage = (rl_performance - cht_performance) / cht_performance
    else:
        performance_ratio = 1.0
        rl_advantage = 0.0
    
    # Check if RL meets expected advantage
    expected_advantage = exp_config['expected_rl_advantage']
    meets_expectation = rl_advantage >= expected_advantage
    
    # Simple statistical significance test
    rl_std = rl_result.get('performance_std', 0.1)
    cht_std = cht_result.get('performance_std', 0.1)
    
    # Simplified t-test approximation
    pooled_std = np.sqrt((rl_std**2 + cht_std**2) / 2)
    t_statistic = abs(rl_performance - cht_performance) / (pooled_std + 1e-8)
    statistical_significance = t_statistic > 2.0  # Rough 95% confidence
    
    success = (meets_expectation and 
              rl_result['convergence_achieved'] and 
              statistical_significance)
    
    return {
        'performance_ratio': performance_ratio,
        'rl_advantage': rl_advantage,
        'absolute_difference': rl_performance - cht_performance,
        'meets_expectation': meets_expectation,
        'expected_advantage': expected_advantage,
        'statistical_significance': statistical_significance,
        't_statistic': t_statistic,
        'convergence_achieved': rl_result['convergence_achieved'],
        'success': success
    }

def generate_tier2_report(results):
    """Generate comprehensive Tier 2 report"""
    
    successful_experiments = [r for r in results.values() if r.get('success', False)]
    total_experiments = len(results)
    
    if successful_experiments:
        rl_advantages = [r['rl_advantage'] for r in successful_experiments]
        average_rl_advantage = np.mean(rl_advantages)
        rl_superior_count = len([r for r in successful_experiments if r['rl_advantage'] > 0])
    else:
        rl_advantages = []
        average_rl_advantage = 0.0
        rl_superior_count = 0
    
    # Analyze by load factor
    load_analysis = {}
    for name, result in results.items():
        if result.get('success', False):
            load_factor = result.get('load_factor', 1.0)
            load_category = 'overloaded' if load_factor > 1.1 else 'underloaded' if load_factor < 0.9 else 'balanced'
            
            if load_category not in load_analysis:
                load_analysis[load_category] = []
            load_analysis[load_category].append(result['rl_advantage'])
    
    # Validation evidence
    rl_outperforms_cht = average_rl_advantage > 0 and rl_superior_count >= total_experiments * 0.6
    consistent_across_scenarios = len(successful_experiments) >= total_experiments * 0.75
    adapts_to_load_conditions = len(load_analysis) >= 2  # Works in multiple load conditions
    supports_paradigm_shift = (rl_outperforms_cht and 
                              consistent_across_scenarios and 
                              adapts_to_load_conditions)
    
    return {
        'timestamp': datetime.now().isoformat(),
        'total_experiments': total_experiments,
        'successful_experiments': len(successful_experiments),
        'success_rate': len(successful_experiments) / total_experiments if total_experiments > 0 else 0,
        'average_rl_advantage': average_rl_advantage,
        'rl_superior_count': rl_superior_count,
        'load_analysis': {k: {'mean_advantage': np.mean(v), 'count': len(v)} 
                         for k, v in load_analysis.items()},
        'validation_evidence': {
            'rl_outperforms_cht': rl_outperforms_cht,
            'consistent_across_scenarios': consistent_across_scenarios,
            'adapts_to_load_conditions': adapts_to_load_conditions,
            'supports_paradigm_shift': supports_paradigm_shift
        },
        'detailed_results': results
    }

if __name__ == "__main__":
    run_tier2_cht_experiments()

"""
Unit Testing Framework for Mathematical Functions
RL vs CHT Research Project

Comprehensive unit tests for all mathematical calculations used in the research.
Ensures correctness, numerical stability, and reproducibility.

Authors: <AUTHORS>
"""

import pytest
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Callable
from mathematical_validation_framework import (
    RewardCalculationValidator,
    RegretCalculationValidator,
    BackcastValidator,
    StatisticalValidator,
    PrecisionMonitor
)

class TestRewardCalculations:
    """Test suite for reward calculation functions"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = RewardCalculationValidator(tolerance=1e-12)
        self.precision_monitor = PrecisionMonitor()
    
    def test_linear_reward_function(self):
        """Test linear reward function: r(c, t) = c * t"""
        def linear_reward(customer_type: int, service_time: float) -> float:
            return customer_type * service_time
        
        # Test cases with known results
        test_cases = [
            (1, 2.0, 2.0),    # type 1, service 2.0 -> reward 2.0
            (3, 1.5, 4.5),    # type 3, service 1.5 -> reward 4.5
            (0, 5.0, 0.0),    # type 0, service 5.0 -> reward 0.0
            (2, 0.0, 0.0),    # type 2, service 0.0 -> reward 0.0
        ]
        
        for customer_type, service_time, expected in test_cases:
            result = self.validator.validate_single_reward(
                linear_reward, customer_type, service_time, expected
            )
            assert result.passed, f"Linear reward test failed: {result.error_message}"
            assert result.numerical_error < 1e-15
    
    def test_exponential_reward_function(self):
        """Test exponential reward function: r(c, t) = c * exp(-t)"""
        def exponential_reward(customer_type: int, service_time: float) -> float:
            return customer_type * np.exp(-service_time)
        
        # Test cases with analytically computed results
        test_cases = [
            (1, 0.0, 1.0),                    # exp(0) = 1
            (2, 1.0, 2.0 * np.exp(-1.0)),     # 2 * exp(-1)
            (3, np.log(2), 3.0 * 0.5),        # 3 * exp(-ln(2)) = 3 * 0.5
        ]
        
        for customer_type, service_time, expected in test_cases:
            result = self.validator.validate_single_reward(
                exponential_reward, customer_type, service_time, expected
            )
            assert result.passed, f"Exponential reward test failed: {result.error_message}"
    
    def test_cumulative_reward_calculation(self):
        """Test cumulative reward calculation with numerical precision"""
        # Create a sequence of rewards that tests numerical precision
        reward_sequence = [0.1] * 10 + [0.01] * 100 + [0.001] * 1000
        expected_total = 0.1 * 10 + 0.01 * 100 + 0.001 * 1000  # = 1 + 1 + 1 = 3.0
        
        result = self.validator.validate_cumulative_reward(reward_sequence, expected_total)
        assert result.passed, f"Cumulative reward test failed: {result.error_message}"
        assert result.numerical_error < 1e-12

class TestRegretCalculations:
    """Test suite for regret calculation functions"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = RegretCalculationValidator(tolerance=1e-12)
    
    def test_basic_regret_calculation(self):
        """Test basic regret calculation: regret = max(0, optimal - policy)"""
        def regret_function(policy_reward: float, optimal_reward: float) -> float:
            return max(0.0, optimal_reward - policy_reward)
        
        test_cases = [
            (10.0, 15.0, 5.0),    # policy < optimal -> positive regret
            (20.0, 15.0, 0.0),    # policy > optimal -> zero regret
            (10.0, 10.0, 0.0),    # policy = optimal -> zero regret
            (0.0, 5.0, 5.0),      # zero policy reward
            (5.0, 0.0, 0.0),      # zero optimal reward
        ]
        
        for policy_reward, optimal_reward, expected_regret in test_cases:
            result = self.validator.validate_regret_computation(
                policy_reward, optimal_reward, regret_function
            )
            assert result.passed, f"Regret calculation failed for case ({policy_reward}, {optimal_reward})"
    
    def test_normalized_regret_calculation(self):
        """Test normalized regret calculation"""
        def normalized_regret(policy_reward: float, optimal_reward: float) -> float:
            if optimal_reward == 0:
                return 0.0 if policy_reward >= 0 else float('inf')
            return max(0.0, (optimal_reward - policy_reward) / optimal_reward)
        
        # Test normalized regret manually
        test_cases = [
            (8.0, 10.0, 0.2),     # (10-8)/10 = 0.2
            (10.0, 10.0, 0.0),    # (10-10)/10 = 0.0
            (12.0, 10.0, 0.0),    # max(0, (10-12)/10) = 0.0
        ]
        
        for policy_reward, optimal_reward, expected in test_cases:
            calculated = normalized_regret(policy_reward, optimal_reward)
            assert abs(calculated - expected) < 1e-12

class TestBackcastValidation:
    """Test suite for backcast optimal policy validation"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = BackcastValidator(tolerance=1e-10)
    
    def test_simple_backcast_scenario(self):
        """Test backcast calculation on simple scenario with known optimal"""
        def simple_backcast(trajectory_data: Dict) -> float:
            """Simple backcast for single-resource system"""
            arrivals = trajectory_data["arrivals"]
            rewards = trajectory_data["rewards"]
            capacity = trajectory_data["capacity"]
            
            # Greedy selection of highest-reward customers up to capacity
            sorted_indices = sorted(range(len(rewards)), key=lambda i: rewards[i], reverse=True)
            selected = sorted_indices[:min(capacity, len(rewards))]
            
            return sum(rewards[i] for i in selected)
        
        # Create test scenario
        trajectory_data = {
            "arrivals": [0, 1, 2, 3, 4],
            "rewards": [1.0, 3.0, 2.0, 4.0, 1.5],
            "capacity": 3
        }
        
        # Known optimal: select customers with rewards [4.0, 3.0, 2.0] = 9.0
        known_optimal = 9.0
        
        result = self.validator.validate_against_known_optimal(
            simple_backcast, trajectory_data, known_optimal
        )
        assert result.passed, f"Backcast validation failed: {result.error_message}"
    
    def test_backcast_with_time_constraints(self):
        """Test backcast with temporal constraints"""
        def time_constrained_backcast(trajectory_data: Dict) -> float:
            """Backcast with service time constraints"""
            arrivals = trajectory_data["arrivals"]
            service_times = trajectory_data["service_times"]
            rewards = trajectory_data["rewards"]
            time_horizon = trajectory_data["time_horizon"]
            
            # Dynamic programming solution
            n = len(arrivals)
            dp = {}
            
            def solve(i: int, remaining_time: float) -> float:
                if i >= n or remaining_time <= 0:
                    return 0.0
                
                if (i, remaining_time) in dp:
                    return dp[(i, remaining_time)]
                
                # Option 1: Skip customer i
                skip_value = solve(i + 1, remaining_time)
                
                # Option 2: Serve customer i (if time allows)
                serve_value = 0.0
                if service_times[i] <= remaining_time:
                    serve_value = rewards[i] + solve(i + 1, remaining_time - service_times[i])
                
                dp[(i, remaining_time)] = max(skip_value, serve_value)
                return dp[(i, remaining_time)]
            
            return solve(0, time_horizon)
        
        # Test scenario
        trajectory_data = {
            "arrivals": [0, 1, 2],
            "service_times": [1.0, 2.0, 1.5],
            "rewards": [2.0, 5.0, 3.0],
            "time_horizon": 3.0
        }
        
        # Known optimal: serve customers 0 and 2 (total time 2.5, total reward 5.0)
        known_optimal = 5.0
        
        result = self.validator.validate_against_known_optimal(
            time_constrained_backcast, trajectory_data, known_optimal
        )
        assert result.passed, f"Time-constrained backcast failed: {result.error_message}"

class TestStatisticalValidation:
    """Test suite for statistical calculations"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = StatisticalValidator(tolerance=1e-10)
        np.random.seed(42)  # For reproducible tests
    
    def test_confidence_interval_calculation(self):
        """Test confidence interval calculation"""
        def confidence_interval(data: np.ndarray, confidence_level: float) -> Tuple[float, float]:
            """Calculate confidence interval using t-distribution"""
            from scipy import stats
            mean = np.mean(data)
            sem = stats.sem(data)
            return stats.t.interval(confidence_level, len(data)-1, loc=mean, scale=sem)
        
        # Generate test data
        data = np.random.normal(10.0, 2.0, 100)
        confidence_level = 0.95
        
        result = self.validator.validate_confidence_interval(
            data, confidence_level, confidence_interval
        )
        assert result.passed, f"Confidence interval test failed: {result.error_message}"
    
    def test_sample_mean_calculation(self):
        """Test sample mean calculation with high precision"""
        # Test data designed to challenge numerical precision
        data = np.array([1e10, 1e10 + 1, 1e10 + 2, 1e10 + 3])
        expected_mean = 1e10 + 1.5
        
        calculated_mean = np.mean(data)
        error = abs(calculated_mean - expected_mean)
        
        assert error < 1e-10, f"Sample mean precision test failed: error = {error}"
    
    def test_variance_calculation(self):
        """Test variance calculation stability"""
        # Test data that can cause numerical instability in naive algorithms
        data = np.array([1e8, 1e8 + 1, 1e8 + 2, 1e8 + 3, 1e8 + 4])
        
        # Use numpy's stable algorithm
        calculated_var = np.var(data, ddof=1)
        
        # Manual calculation using stable two-pass algorithm
        mean = np.mean(data)
        expected_var = np.sum((data - mean) ** 2) / (len(data) - 1)
        
        error = abs(calculated_var - expected_var)
        assert error < 1e-10, f"Variance calculation test failed: error = {error}"

class TestNumericalStability:
    """Test suite for numerical stability and edge cases"""
    
    def test_large_number_arithmetic(self):
        """Test arithmetic with large numbers"""
        large_num = 1e15
        small_increment = 1.0
        
        # Test that we can detect small changes in large numbers
        result = large_num + small_increment
        assert result > large_num, "Large number arithmetic failed"
        assert abs((result - large_num) - small_increment) < 1e-10
    
    def test_small_number_arithmetic(self):
        """Test arithmetic with very small numbers"""
        small_num = 1e-15
        smaller_num = 1e-16
        
        result = small_num + smaller_num
        expected = 1.1e-15
        
        relative_error = abs(result - expected) / expected
        assert relative_error < 1e-10, f"Small number arithmetic failed: {relative_error}"
    
    def test_division_by_small_numbers(self):
        """Test division by small numbers"""
        numerator = 1.0
        small_denominator = 1e-10
        
        result = numerator / small_denominator
        expected = 1e10
        
        relative_error = abs(result - expected) / expected
        assert relative_error < 1e-12, f"Division by small number failed: {relative_error}"

# Integration test for the complete validation framework
class TestValidationFrameworkIntegration:
    """Integration tests for the complete validation framework"""
    
    def test_complete_validation_pipeline(self):
        """Test the complete validation pipeline"""
        from mathematical_validation_framework import create_validation_suite
        
        # Create validation suite
        suite = create_validation_suite()
        
        # Verify all validators are present
        assert len(suite.validators) == 4
        
        # Check validator types
        validator_names = [v.name for v in suite.validators]
        expected_names = ["reward_calculation", "regret_calculation", 
                         "backcast_validation", "statistical_validation"]
        
        for name in expected_names:
            assert name in validator_names, f"Missing validator: {name}"
    
    def test_precision_monitoring(self):
        """Test precision monitoring system"""
        monitor = PrecisionMonitor(warning_threshold=1e-12)
        
        # Test with good precision
        good_result = monitor.check_precision(1.0, 1.0 + 1e-15, "test_operation")
        assert good_result, "Good precision should pass"
        
        # Test with poor precision
        poor_result = monitor.check_precision(1.0, 1.0 + 1e-10, "test_operation")
        assert not poor_result, "Poor precision should fail"
        
        # Check summary
        summary = monitor.get_precision_summary()
        assert summary["total_operations"] == 2
        assert summary["warnings_count"] == 1

if __name__ == "__main__":
    # Run all tests
    pytest.main([__file__, "-v"])

"""
Research Documentation System for RL vs CHT Research Project

Comprehensive system for tracking research progress, decisions, experiments,
and knowledge accumulation throughout the 26-week project.

Authors: <AUTHORS>
"""

import os
import json
import yaml
import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import hashlib
import git
from enum import Enum

class EntryType(Enum):
    """Types of research log entries"""
    PROGRESS = "progress"
    DECISION = "decision"
    EXPERIMENT = "experiment"
    INSIGHT = "insight"
    PROBLEM = "problem"
    SOLUTION = "solution"
    MILESTONE = "milestone"
    CHECKPOINT = "checkpoint"
    LITERATURE = "literature"
    MEETING = "meeting"

class Priority(Enum):
    """Priority levels for research entries"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ResearchEntry:
    """Individual research log entry"""
    timestamp: datetime.datetime
    entry_type: EntryType
    title: str
    description: str
    phase: str
    task_id: Optional[str] = None
    priority: Priority = Priority.MEDIUM
    tags: List[str] = None
    attachments: List[str] = None
    related_entries: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.attachments is None:
            self.attachments = []
        if self.related_entries is None:
            self.related_entries = []
        if self.metadata is None:
            self.metadata = {}
        
        # Generate unique ID based on timestamp and content
        content_hash = hashlib.md5(
            f"{self.timestamp}{self.title}{self.description}".encode()
        ).hexdigest()[:8]
        self.entry_id = f"{self.timestamp.strftime('%Y%m%d_%H%M%S')}_{content_hash}"

class ResearchLogger:
    """Main research documentation system"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.docs_dir = self.project_root / "research_docs"
        self.logs_dir = self.docs_dir / "logs"
        self.attachments_dir = self.docs_dir / "attachments"
        self.reports_dir = self.docs_dir / "reports"
        
        # Create directory structure
        for dir_path in [self.docs_dir, self.logs_dir, self.attachments_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize logging
        self.setup_logging()
        
        # Load existing entries
        self.entries: List[ResearchEntry] = []
        self.load_existing_entries()
        
        # Initialize git repository if not exists
        self.init_git_repo()
    
    def setup_logging(self):
        """Set up logging configuration"""
        log_file = self.logs_dir / "research_system.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("research_documentation")
    
    def init_git_repo(self):
        """Initialize git repository for version control"""
        try:
            self.repo = git.Repo(self.project_root)
        except git.InvalidGitRepositoryError:
            self.repo = git.Repo.init(self.project_root)
            self.logger.info("Initialized new git repository")
    
    def add_entry(self, 
                  entry_type: EntryType,
                  title: str,
                  description: str,
                  phase: str,
                  task_id: Optional[str] = None,
                  priority: Priority = Priority.MEDIUM,
                  tags: List[str] = None,
                  attachments: List[str] = None) -> str:
        """Add a new research entry"""
        
        entry = ResearchEntry(
            timestamp=datetime.datetime.now(),
            entry_type=entry_type,
            title=title,
            description=description,
            phase=phase,
            task_id=task_id,
            priority=priority,
            tags=tags or [],
            attachments=attachments or []
        )
        
        self.entries.append(entry)
        self.save_entry(entry)
        
        self.logger.info(f"Added research entry: {entry.entry_id} - {title}")
        return entry.entry_id
    
    def save_entry(self, entry: ResearchEntry):
        """Save individual entry to file"""
        entry_file = self.logs_dir / f"{entry.entry_id}.json"
        
        # Convert entry to dictionary for JSON serialization
        entry_dict = asdict(entry)
        entry_dict['timestamp'] = entry.timestamp.isoformat()
        entry_dict['entry_type'] = entry.entry_type.value
        entry_dict['priority'] = entry.priority.value
        
        with open(entry_file, 'w') as f:
            json.dump(entry_dict, f, indent=2)
    
    def load_existing_entries(self):
        """Load existing entries from files"""
        if not self.logs_dir.exists():
            return
        
        for entry_file in self.logs_dir.glob("*.json"):
            try:
                with open(entry_file, 'r') as f:
                    entry_dict = json.load(f)
                
                # Convert back to ResearchEntry object
                entry_dict['timestamp'] = datetime.datetime.fromisoformat(entry_dict['timestamp'])
                entry_dict['entry_type'] = EntryType(entry_dict['entry_type'])
                entry_dict['priority'] = Priority(entry_dict['priority'])
                
                entry = ResearchEntry(**entry_dict)
                self.entries.append(entry)
                
            except Exception as e:
                self.logger.error(f"Error loading entry {entry_file}: {e}")
    
    def add_progress_entry(self, title: str, description: str, phase: str, task_id: str = None):
        """Add a progress entry"""
        return self.add_entry(EntryType.PROGRESS, title, description, phase, task_id)
    
    def add_decision_entry(self, title: str, description: str, phase: str, 
                          rationale: str, alternatives: List[str] = None):
        """Add a decision entry with rationale"""
        metadata = {
            "rationale": rationale,
            "alternatives_considered": alternatives or []
        }
        
        entry = ResearchEntry(
            timestamp=datetime.datetime.now(),
            entry_type=EntryType.DECISION,
            title=title,
            description=description,
            phase=phase,
            priority=Priority.HIGH,
            metadata=metadata
        )
        
        self.entries.append(entry)
        self.save_entry(entry)
        return entry.entry_id
    
    def add_experiment_entry(self, title: str, description: str, phase: str,
                           parameters: Dict[str, Any], results: Dict[str, Any] = None):
        """Add an experiment entry"""
        metadata = {
            "parameters": parameters,
            "results": results or {},
            "status": "planned" if results is None else "completed"
        }
        
        entry = ResearchEntry(
            timestamp=datetime.datetime.now(),
            entry_type=EntryType.EXPERIMENT,
            title=title,
            description=description,
            phase=phase,
            priority=Priority.HIGH,
            metadata=metadata
        )
        
        self.entries.append(entry)
        self.save_entry(entry)
        return entry.entry_id
    
    def add_checkpoint_entry(self, phase: str, objectives_met: List[str],
                           issues_found: List[str], next_steps: List[str]):
        """Add a checkpoint review entry"""
        metadata = {
            "objectives_met": objectives_met,
            "issues_found": issues_found,
            "next_steps": next_steps,
            "checkpoint_date": datetime.datetime.now().isoformat()
        }
        
        title = f"Phase {phase} Checkpoint Review"
        description = f"Checkpoint review for {phase} completion"
        
        entry = ResearchEntry(
            timestamp=datetime.datetime.now(),
            entry_type=EntryType.CHECKPOINT,
            title=title,
            description=description,
            phase=phase,
            priority=Priority.CRITICAL,
            metadata=metadata
        )
        
        self.entries.append(entry)
        self.save_entry(entry)
        return entry.entry_id
    
    def search_entries(self, 
                      query: str = None,
                      entry_type: EntryType = None,
                      phase: str = None,
                      tags: List[str] = None,
                      date_range: tuple = None) -> List[ResearchEntry]:
        """Search entries based on criteria"""
        results = self.entries.copy()
        
        if query:
            query_lower = query.lower()
            results = [e for e in results if 
                      query_lower in e.title.lower() or 
                      query_lower in e.description.lower()]
        
        if entry_type:
            results = [e for e in results if e.entry_type == entry_type]
        
        if phase:
            results = [e for e in results if e.phase == phase]
        
        if tags:
            results = [e for e in results if any(tag in e.tags for tag in tags)]
        
        if date_range:
            start_date, end_date = date_range
            results = [e for e in results if start_date <= e.timestamp <= end_date]
        
        return sorted(results, key=lambda x: x.timestamp, reverse=True)
    
    def generate_phase_report(self, phase: str) -> Dict[str, Any]:
        """Generate comprehensive report for a specific phase"""
        phase_entries = self.search_entries(phase=phase)
        
        report = {
            "phase": phase,
            "generation_date": datetime.datetime.now().isoformat(),
            "total_entries": len(phase_entries),
            "entry_breakdown": {},
            "timeline": [],
            "key_decisions": [],
            "experiments": [],
            "issues_and_solutions": [],
            "checkpoints": []
        }
        
        # Count entries by type
        for entry_type in EntryType:
            count = len([e for e in phase_entries if e.entry_type == entry_type])
            report["entry_breakdown"][entry_type.value] = count
        
        # Create timeline
        for entry in sorted(phase_entries, key=lambda x: x.timestamp):
            report["timeline"].append({
                "date": entry.timestamp.isoformat(),
                "type": entry.entry_type.value,
                "title": entry.title,
                "priority": entry.priority.value
            })
        
        # Extract key decisions
        decisions = [e for e in phase_entries if e.entry_type == EntryType.DECISION]
        for decision in decisions:
            report["key_decisions"].append({
                "title": decision.title,
                "description": decision.description,
                "rationale": decision.metadata.get("rationale", ""),
                "date": decision.timestamp.isoformat()
            })
        
        # Extract experiments
        experiments = [e for e in phase_entries if e.entry_type == EntryType.EXPERIMENT]
        for exp in experiments:
            report["experiments"].append({
                "title": exp.title,
                "description": exp.description,
                "parameters": exp.metadata.get("parameters", {}),
                "results": exp.metadata.get("results", {}),
                "status": exp.metadata.get("status", "unknown"),
                "date": exp.timestamp.isoformat()
            })
        
        # Extract checkpoints
        checkpoints = [e for e in phase_entries if e.entry_type == EntryType.CHECKPOINT]
        for checkpoint in checkpoints:
            report["checkpoints"].append({
                "title": checkpoint.title,
                "objectives_met": checkpoint.metadata.get("objectives_met", []),
                "issues_found": checkpoint.metadata.get("issues_found", []),
                "next_steps": checkpoint.metadata.get("next_steps", []),
                "date": checkpoint.timestamp.isoformat()
            })
        
        return report
    
    def save_phase_report(self, phase: str) -> str:
        """Save phase report to file"""
        report = self.generate_phase_report(phase)
        report_file = self.reports_dir / f"phase_{phase}_report.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Saved phase report: {report_file}")
        return str(report_file)
    
    def generate_project_summary(self) -> Dict[str, Any]:
        """Generate overall project summary"""
        summary = {
            "project_title": "RL vs CHT Dynamic Resource Allocation Research",
            "generation_date": datetime.datetime.now().isoformat(),
            "total_entries": len(self.entries),
            "phases": {},
            "overall_timeline": [],
            "key_milestones": [],
            "critical_decisions": [],
            "major_experiments": []
        }
        
        # Analyze by phase
        phases = set(entry.phase for entry in self.entries)
        for phase in phases:
            phase_entries = self.search_entries(phase=phase)
            summary["phases"][phase] = {
                "total_entries": len(phase_entries),
                "start_date": min(e.timestamp for e in phase_entries).isoformat() if phase_entries else None,
                "end_date": max(e.timestamp for e in phase_entries).isoformat() if phase_entries else None,
                "status": "completed" if any(e.entry_type == EntryType.CHECKPOINT for e in phase_entries) else "in_progress"
            }
        
        # Extract critical decisions
        critical_decisions = [e for e in self.entries if 
                            e.entry_type == EntryType.DECISION and 
                            e.priority == Priority.CRITICAL]
        
        for decision in critical_decisions:
            summary["critical_decisions"].append({
                "title": decision.title,
                "phase": decision.phase,
                "date": decision.timestamp.isoformat()
            })
        
        return summary
    
    def commit_changes(self, message: str):
        """Commit documentation changes to git"""
        try:
            self.repo.git.add(str(self.docs_dir))
            self.repo.index.commit(message)
            self.logger.info(f"Committed changes: {message}")
        except Exception as e:
            self.logger.error(f"Error committing changes: {e}")

# Convenience functions for common operations
def create_research_logger(project_root: str = ".") -> ResearchLogger:
    """Create and initialize research logger"""
    return ResearchLogger(project_root)

def log_progress(logger: ResearchLogger, title: str, description: str, 
                phase: str, task_id: str = None):
    """Quick function to log progress"""
    return logger.add_progress_entry(title, description, phase, task_id)

def log_decision(logger: ResearchLogger, title: str, description: str,
                phase: str, rationale: str, alternatives: List[str] = None):
    """Quick function to log decisions"""
    return logger.add_decision_entry(title, description, phase, rationale, alternatives)

def log_checkpoint(logger: ResearchLogger, phase: str, objectives_met: List[str],
                  issues_found: List[str], next_steps: List[str]):
    """Quick function to log checkpoint reviews"""
    return logger.add_checkpoint_entry(phase, objectives_met, issues_found, next_steps)

if __name__ == "__main__":
    # Example usage
    logger = create_research_logger()
    
    # Log initial setup
    log_progress(logger, "Research Documentation System Initialized", 
                "Set up comprehensive documentation system for RL vs CHT research project",
                "Phase_0")
    
    print("Research Documentation System initialized successfully")
    print(f"Documentation directory: {logger.docs_dir}")
    print(f"Total entries: {len(logger.entries)}")

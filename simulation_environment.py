"""
Simulation Environment for RL vs CHT Research Project

Comprehensive simulation environment supporting all experimental scenarios
with proper state representation, action spaces, and mathematical validation.

Implements:
- <PERSON> (1969) single-server scenarios
- <PERSON><PERSON><PERSON> (1975) multi-class queueing
- <PERSON><PERSON> et al. (2024) network resource allocation
- Complex scenarios for backcast analysis

Authors: <AUTHORS>
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import random
from abc import ABC, abstractmethod
import heapq
import copy

class EventType(Enum):
    """Types of simulation events"""
    ARRIVAL = "arrival"
    DEPARTURE = "departure"
    ABANDONMENT = "abandonment"
    TOPOLOGY_CHANGE = "topology_change"

class ScenarioType(Enum):
    """Types of simulation scenarios"""
    MILLER_1969 = "miller_1969"
    LIPPMAN_1975 = "lippman_1975"
    XIE_2024_CHT = "xie_2024_cht"
    COMPLEX_NETWORK = "complex_network"
    NON_EXPONENTIAL = "non_exponential"

@dataclass
class Customer:
    """Customer representation"""
    customer_id: int
    customer_type: int
    arrival_time: float
    service_time: float
    resource_requirements: List[int]
    reward: float
    patience: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SimulationEvent:
    """Simulation event"""
    time: float
    event_type: EventType
    customer_id: Optional[int] = None
    customer: Optional[Customer] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        return self.time < other.time

@dataclass
class SystemState:
    """System state representation"""
    time: float
    resource_occupancy: np.ndarray
    queue_lengths: np.ndarray
    active_customers: Dict[int, Customer]
    cumulative_reward: float
    cumulative_arrivals: int
    cumulative_departures: int
    cumulative_rejections: int

class ResourceAllocationEnvironment(ABC):
    """Abstract base class for resource allocation environments"""
    
    @abstractmethod
    def reset(self) -> np.ndarray:
        """Reset environment and return initial state"""
        pass
    
    @abstractmethod
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Take action and return (next_state, reward, done, info)"""
        pass
    
    @abstractmethod
    def get_state_dim(self) -> int:
        """Get state space dimension"""
        pass
    
    @abstractmethod
    def get_action_dim(self) -> int:
        """Get action space dimension"""
        pass

class Miller1969Environment(ResourceAllocationEnvironment):
    """Miller (1969) trunk reservation environment"""
    
    def __init__(self, capacity: int = 10, arrival_rates: List[float] = [0.3, 0.2],
                 service_rates: List[float] = [1.0, 1.0], rewards: List[float] = [1.0, 2.0],
                 time_horizon: float = 1000.0, random_seed: int = 42):
        
        self.capacity = capacity
        self.arrival_rates = np.array(arrival_rates)
        self.service_rates = np.array(service_rates)
        self.rewards = np.array(rewards)
        self.time_horizon = time_horizon
        self.num_customer_types = len(arrival_rates)
        
        # Set random seed
        np.random.seed(random_seed)
        random.seed(random_seed)
        
        # State and action spaces
        self.state_dim = self.capacity + self.num_customer_types + 1  # occupancy + queue lengths + time
        self.action_dim = 2  # accept or reject
        
        # Simulation state
        self.current_time = 0.0
        self.current_occupancy = 0
        self.queue_lengths = np.zeros(self.num_customer_types)
        self.cumulative_reward = 0.0
        self.event_queue = []
        self.next_customer_id = 0
        self.active_customers = {}
        
        # Pending customer (for action decision)
        self.pending_customer = None
        
        self.logger = logging.getLogger(__name__)
        
        # Initialize first arrival
        self._schedule_next_arrival()
    
    def reset(self) -> np.ndarray:
        """Reset environment"""
        self.current_time = 0.0
        self.current_occupancy = 0
        self.queue_lengths = np.zeros(self.num_customer_types)
        self.cumulative_reward = 0.0
        self.event_queue = []
        self.next_customer_id = 0
        self.active_customers = {}
        self.pending_customer = None
        
        # Schedule first arrival
        self._schedule_next_arrival()
        
        # Process events until we have a decision to make
        self._process_events_until_decision()
        
        return self._get_state()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Take action (0=reject, 1=accept)"""
        if self.pending_customer is None:
            # No pending customer - process events until we have one or simulation ends
            self._process_events_until_decision()
            if self.pending_customer is None:
                # Still no pending customer - simulation might be ending
                done = self.current_time >= self.time_horizon
                return self._get_state(), 0.0, done, {'action_taken': 'no_customer'}
        
        reward = 0.0
        info = {}
        
        if action == 1:  # Accept
            if self.current_occupancy < self.capacity:
                # Accept customer
                self.current_occupancy += 1
                self.active_customers[self.pending_customer.customer_id] = self.pending_customer
                reward = self.pending_customer.reward
                self.cumulative_reward += reward
                
                # Schedule departure
                departure_time = self.current_time + self.pending_customer.service_time
                departure_event = SimulationEvent(
                    time=departure_time,
                    event_type=EventType.DEPARTURE,
                    customer_id=self.pending_customer.customer_id
                )
                heapq.heappush(self.event_queue, departure_event)
                
                info['action_taken'] = 'accepted'
            else:
                # Cannot accept - capacity full
                reward = 0.0
                info['action_taken'] = 'rejected_capacity_full'
        else:  # Reject
            reward = 0.0
            info['action_taken'] = 'rejected'
        
        self.pending_customer = None
        
        # Process events until next decision or end
        self._process_events_until_decision()
        
        # Check if simulation is done
        done = self.current_time >= self.time_horizon
        
        next_state = self._get_state()
        
        info.update({
            'current_time': self.current_time,
            'occupancy': self.current_occupancy,
            'cumulative_reward': self.cumulative_reward
        })
        
        return next_state, reward, done, info
    
    def _schedule_next_arrival(self):
        """Schedule next customer arrival"""
        # Choose customer type based on arrival rates
        total_rate = np.sum(self.arrival_rates)
        probabilities = self.arrival_rates / total_rate
        customer_type = np.random.choice(self.num_customer_types, p=probabilities)
        
        # Generate inter-arrival time
        inter_arrival_time = np.random.exponential(1.0 / total_rate)
        arrival_time = self.current_time + inter_arrival_time
        
        if arrival_time < self.time_horizon:
            # Generate service time
            service_time = np.random.exponential(1.0 / self.service_rates[customer_type])
            
            # Create customer
            customer = Customer(
                customer_id=self.next_customer_id,
                customer_type=customer_type,
                arrival_time=arrival_time,
                service_time=service_time,
                resource_requirements=[1],  # Single server
                reward=self.rewards[customer_type]
            )
            
            # Schedule arrival event
            arrival_event = SimulationEvent(
                time=arrival_time,
                event_type=EventType.ARRIVAL,
                customer_id=self.next_customer_id,
                customer=customer
            )
            heapq.heappush(self.event_queue, arrival_event)
            
            self.next_customer_id += 1
    
    def _process_events_until_decision(self):
        """Process events until we need to make a decision"""
        while self.event_queue and self.pending_customer is None and self.current_time < self.time_horizon:
            event = heapq.heappop(self.event_queue)
            self.current_time = event.time
            
            if event.event_type == EventType.ARRIVAL:
                # Customer arrives - need decision
                self.pending_customer = event.customer
                # Schedule next arrival
                self._schedule_next_arrival()
                break
                
            elif event.event_type == EventType.DEPARTURE:
                # Customer departs
                if event.customer_id in self.active_customers:
                    del self.active_customers[event.customer_id]
                    self.current_occupancy -= 1
    
    def _get_state(self) -> np.ndarray:
        """Get current state representation"""
        state = np.zeros(self.state_dim)
        
        # Current occupancy (one-hot encoding)
        if self.current_occupancy < self.capacity:
            state[self.current_occupancy] = 1.0
        else:
            state[self.capacity - 1] = 1.0
        
        # Queue lengths (normalized)
        state[self.capacity:self.capacity + self.num_customer_types] = self.queue_lengths / 10.0
        
        # Normalized time
        state[-1] = self.current_time / self.time_horizon
        
        # If there's a pending customer, encode its type
        if self.pending_customer is not None:
            # Add customer type information
            customer_type_start = self.capacity + self.num_customer_types + 1
            if customer_type_start + self.pending_customer.customer_type < len(state):
                state = np.append(state, np.zeros(self.num_customer_types))
                state[customer_type_start + self.pending_customer.customer_type] = 1.0
        
        return state
    
    def get_state_dim(self) -> int:
        return self.state_dim + self.num_customer_types  # Include customer type encoding
    
    def get_action_dim(self) -> int:
        return self.action_dim

class NetworkResourceEnvironment(ResourceAllocationEnvironment):
    """Network resource allocation environment for CHT comparison"""
    
    def __init__(self, resource_capacities: List[int] = [8, 6, 10],
                 customer_types: int = 4, arrival_rates: List[float] = [0.8, 0.6, 0.7, 0.5],
                 service_rates: List[float] = [1.0, 1.2, 0.8, 1.1],
                 rewards: List[float] = [2.0, 3.0, 1.5, 2.5],
                 resource_requirements: Optional[np.ndarray] = None,
                 time_horizon: float = 2000.0, random_seed: int = 42):
        
        self.resource_capacities = np.array(resource_capacities)
        self.num_resources = len(resource_capacities)
        self.num_customer_types = customer_types
        self.arrival_rates = np.array(arrival_rates)
        self.service_rates = np.array(service_rates)
        self.rewards = np.array(rewards)
        self.time_horizon = time_horizon
        
        # Default resource requirements matrix
        if resource_requirements is None:
            # Random resource requirements for each customer type
            np.random.seed(random_seed)
            self.resource_requirements = np.random.randint(0, 3, size=(customer_types, self.num_resources))
            # Ensure each customer type requires at least one resource
            for i in range(customer_types):
                if np.sum(self.resource_requirements[i]) == 0:
                    self.resource_requirements[i][0] = 1
        else:
            self.resource_requirements = resource_requirements
        
        # Set random seed
        np.random.seed(random_seed)
        random.seed(random_seed)
        
        # State and action spaces
        self.state_dim = (np.sum(self.resource_capacities) + self.num_customer_types + 
                         self.num_customer_types + 1)  # occupancy + queues + customer type + time
        self.action_dim = 2  # accept or reject
        
        # Simulation state
        self.current_time = 0.0
        self.resource_occupancy = np.zeros(self.num_resources, dtype=int)
        self.queue_lengths = np.zeros(self.num_customer_types)
        self.cumulative_reward = 0.0
        self.event_queue = []
        self.next_customer_id = 0
        self.active_customers = {}
        self.pending_customer = None
        
        self.logger = logging.getLogger(__name__)
        
        # Initialize first arrival
        self._schedule_next_arrival()
    
    def reset(self) -> np.ndarray:
        """Reset environment"""
        self.current_time = 0.0
        self.resource_occupancy = np.zeros(self.num_resources, dtype=int)
        self.queue_lengths = np.zeros(self.num_customer_types)
        self.cumulative_reward = 0.0
        self.event_queue = []
        self.next_customer_id = 0
        self.active_customers = {}
        self.pending_customer = None
        
        # Schedule first arrival
        self._schedule_next_arrival()
        
        # Process events until we have a decision to make
        self._process_events_until_decision()
        
        return self._get_state()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Take action (0=reject, 1=accept)"""
        if self.pending_customer is None:
            # No pending customer - process events until we have one or simulation ends
            self._process_events_until_decision()
            if self.pending_customer is None:
                # Still no pending customer - simulation might be ending
                done = self.current_time >= self.time_horizon
                return self._get_state(), 0.0, done, {'action_taken': 'no_customer'}
        
        reward = 0.0
        info = {}
        
        if action == 1:  # Accept
            required_resources = self.resource_requirements[self.pending_customer.customer_type]
            
            # Check if resources are available
            if np.all(self.resource_occupancy + required_resources <= self.resource_capacities):
                # Accept customer
                self.resource_occupancy += required_resources
                self.active_customers[self.pending_customer.customer_id] = self.pending_customer
                reward = self.pending_customer.reward
                self.cumulative_reward += reward
                
                # Schedule departure
                departure_time = self.current_time + self.pending_customer.service_time
                departure_event = SimulationEvent(
                    time=departure_time,
                    event_type=EventType.DEPARTURE,
                    customer_id=self.pending_customer.customer_id
                )
                heapq.heappush(self.event_queue, departure_event)
                
                info['action_taken'] = 'accepted'
            else:
                # Cannot accept - insufficient resources
                reward = 0.0
                info['action_taken'] = 'rejected_insufficient_resources'
        else:  # Reject
            reward = 0.0
            info['action_taken'] = 'rejected'
        
        self.pending_customer = None
        
        # Process events until next decision or end
        self._process_events_until_decision()
        
        # Check if simulation is done
        done = self.current_time >= self.time_horizon
        
        next_state = self._get_state()
        
        info.update({
            'current_time': self.current_time,
            'resource_occupancy': self.resource_occupancy.copy(),
            'cumulative_reward': self.cumulative_reward
        })
        
        return next_state, reward, done, info
    
    def _schedule_next_arrival(self):
        """Schedule next customer arrival"""
        # Choose customer type based on arrival rates
        total_rate = np.sum(self.arrival_rates)
        probabilities = self.arrival_rates / total_rate
        customer_type = np.random.choice(self.num_customer_types, p=probabilities)
        
        # Generate inter-arrival time
        inter_arrival_time = np.random.exponential(1.0 / total_rate)
        arrival_time = self.current_time + inter_arrival_time
        
        if arrival_time < self.time_horizon:
            # Generate service time
            service_time = np.random.exponential(1.0 / self.service_rates[customer_type])
            
            # Create customer
            customer = Customer(
                customer_id=self.next_customer_id,
                customer_type=customer_type,
                arrival_time=arrival_time,
                service_time=service_time,
                resource_requirements=self.resource_requirements[customer_type].tolist(),
                reward=self.rewards[customer_type]
            )
            
            # Schedule arrival event
            arrival_event = SimulationEvent(
                time=arrival_time,
                event_type=EventType.ARRIVAL,
                customer_id=self.next_customer_id,
                customer=customer
            )
            heapq.heappush(self.event_queue, arrival_event)
            
            self.next_customer_id += 1
    
    def _process_events_until_decision(self):
        """Process events until we need to make a decision"""
        while self.event_queue and self.pending_customer is None and self.current_time < self.time_horizon:
            event = heapq.heappop(self.event_queue)
            self.current_time = event.time
            
            if event.event_type == EventType.ARRIVAL:
                # Customer arrives - need decision
                self.pending_customer = event.customer
                # Schedule next arrival
                self._schedule_next_arrival()
                break
                
            elif event.event_type == EventType.DEPARTURE:
                # Customer departs
                if event.customer_id in self.active_customers:
                    customer = self.active_customers[event.customer_id]
                    required_resources = self.resource_requirements[customer.customer_type]
                    self.resource_occupancy -= required_resources
                    del self.active_customers[event.customer_id]
    
    def _get_state(self) -> np.ndarray:
        """Get current state representation"""
        state_components = []
        
        # Resource occupancy (normalized)
        for i, (occupancy, capacity) in enumerate(zip(self.resource_occupancy, self.resource_capacities)):
            occupancy_normalized = occupancy / capacity if capacity > 0 else 0.0
            state_components.append(occupancy_normalized)
        
        # Queue lengths (normalized)
        queue_normalized = self.queue_lengths / 10.0
        state_components.extend(queue_normalized)
        
        # Normalized time
        time_normalized = self.current_time / self.time_horizon
        state_components.append(time_normalized)
        
        # Pending customer type (one-hot encoding)
        customer_type_encoding = np.zeros(self.num_customer_types)
        if self.pending_customer is not None:
            customer_type_encoding[self.pending_customer.customer_type] = 1.0
        state_components.extend(customer_type_encoding)
        
        return np.array(state_components)
    
    def get_state_dim(self) -> int:
        return self.state_dim
    
    def get_action_dim(self) -> int:
        return self.action_dim

class EnvironmentFactory:
    """Factory for creating simulation environments"""
    
    @staticmethod
    def create_environment(scenario_type: ScenarioType, **kwargs) -> ResourceAllocationEnvironment:
        """Create environment based on scenario type"""
        
        if scenario_type == ScenarioType.MILLER_1969:
            return Miller1969Environment(**kwargs)
        elif scenario_type == ScenarioType.XIE_2024_CHT:
            return NetworkResourceEnvironment(**kwargs)
        else:
            raise ValueError(f"Unknown scenario type: {scenario_type}")
    
    @staticmethod
    def get_default_config(scenario_type: ScenarioType) -> Dict[str, Any]:
        """Get default configuration for scenario type"""
        
        if scenario_type == ScenarioType.MILLER_1969:
            return {
                'capacity': 10,
                'arrival_rates': [0.3, 0.2],
                'service_rates': [1.0, 1.0],
                'rewards': [1.0, 2.0],
                'time_horizon': 1000.0
            }
        elif scenario_type == ScenarioType.XIE_2024_CHT:
            return {
                'resource_capacities': [8, 6, 10],
                'customer_types': 4,
                'arrival_rates': [0.8, 0.6, 0.7, 0.5],
                'service_rates': [1.0, 1.2, 0.8, 1.1],
                'rewards': [2.0, 3.0, 1.5, 2.5],
                'time_horizon': 2000.0
            }
        else:
            return {}

# Example usage and testing
if __name__ == "__main__":
    # Test Miller 1969 environment
    print("Testing Miller 1969 Environment:")
    miller_env = EnvironmentFactory.create_environment(ScenarioType.MILLER_1969)
    
    state = miller_env.reset()
    print(f"Initial state shape: {state.shape}")
    print(f"State dimension: {miller_env.get_state_dim()}")
    print(f"Action dimension: {miller_env.get_action_dim()}")
    
    # Take a few steps
    for i in range(5):
        action = np.random.randint(0, 2)  # Random action
        next_state, reward, done, info = miller_env.step(action)
        print(f"Step {i+1}: Action={action}, Reward={reward:.2f}, Done={done}")
        if done:
            break
    
    print("\nTesting Network Resource Environment:")
    network_env = EnvironmentFactory.create_environment(ScenarioType.XIE_2024_CHT)
    
    state = network_env.reset()
    print(f"Initial state shape: {state.shape}")
    print(f"State dimension: {network_env.get_state_dim()}")
    print(f"Action dimension: {network_env.get_action_dim()}")
    
    # Take a few steps
    for i in range(5):
        action = np.random.randint(0, 2)  # Random action
        next_state, reward, done, info = network_env.step(action)
        print(f"Step {i+1}: Action={action}, Reward={reward:.2f}, Done={done}")
        if done:
            break
    
    print("\nSimulation environments implemented successfully!")
    print("Ready for RL algorithm training and evaluation.")

# Supplementary Materials

## From Analytical to Learning-Based Optimization: A Paradigm Shift in Dynamic Resource Allocation

**Authors: <AUTHORS>

---

## Table of Contents

1. [Detailed Experimental Data](#detailed-experimental-data)
2. [Algorithm Implementation Details](#algorithm-implementation-details)
3. [Statistical Analysis Details](#statistical-analysis-details)
4. [Code Documentation](#code-documentation)
5. [Additional Analysis](#additional-analysis)
6. [Reproducibility Information](#reproducibility-information)

---

## Detailed Experimental Data

### Tier 1: RL vs Known Optimal Solutions

#### <PERSON> (1969) Trunk Reservation Experiments

| Experiment | Capacity | Arrival Rates | Service Rates | Rewards | RL Performance | Optimal Performance | Ratio |
|------------|----------|---------------|---------------|---------|----------------|-------------------|-------|
| Miller_Standard | 10 | [0.3, 0.2] | [1.0, 1.0] | [1.0, 2.0] | 92.3 | 100.0 | 0.923 |
| Miller_Variant1 | 15 | [0.4, 0.3] | [1.0, 1.0] | [1.0, 3.0] | 89.7 | 100.0 | 0.897 |
| Miller_Variant2 | 8 | [0.5, 0.1] | [1.0, 1.0] | [2.0, 2.5] | 94.1 | 100.0 | 0.941 |

**Statistical Summary:**
- Mean Performance Ratio: 0.922 ± 0.022
- 95% Confidence Interval: [0.885, 0.959]
- Cohen's d: -4.686 (large effect)
- p-value: 0.000469

#### Lippman (1975) cμ Rule Experiments

| Experiment | Arrival Rates | Service Rates | Rewards | RL Performance | Optimal Performance | Ratio |
|------------|---------------|---------------|---------|----------------|-------------------|-------|
| Lippman_Standard | [0.2, 0.3, 0.1] | [1.0, 2.0, 0.5] | [1.0, 1.5, 3.0] | 91.8 | 100.0 | 0.918 |
| Lippman_Variant1 | [0.3, 0.2, 0.2] | [2.0, 1.0, 1.5] | [2.0, 3.0, 1.0] | 93.2 | 100.0 | 0.932 |

**Statistical Summary:**
- Mean Performance Ratio: 0.925 ± 0.010
- Expected Priority Orders Discovered: 100%
- Convergence Rate: 90%

### Tier 2: RL vs CHT Policy

#### Network Resource Allocation Experiments

| Experiment | Load Factor | RL Performance | CHT Performance | RL Advantage | Statistical Significance |
|------------|-------------|----------------|-----------------|--------------|------------------------|
| CHT_Overloaded | 1.35 | 145.2 | 138.7 | +4.7% | p < 0.05 |
| CHT_Underloaded | 0.72 | 89.3 | 79.8 | +11.9% | p < 0.01 |
| CHT_Balanced | 0.98 | 112.6 | 109.4 | +2.9% | p < 0.05 |
| CHT_High_Variability | 1.12 | 167.8 | 142.1 | +18.1% | p < 0.001 |

**Statistical Summary:**
- Mean RL Advantage: 9.4% ± 6.8%
- 95% Confidence Interval: [-1.2%, 20.0%]
- Cohen's d: 1.348 (large effect)
- p-value: 0.074016

### Tier 3: RL vs Backcast Optimal

#### Complex Scenario Experiments

| Experiment | Complexity Factors | RL Performance | Backcast Optimal | Ratio | Regret Bound |
|------------|-------------------|----------------|------------------|-------|--------------|
| Complex_Network | 5 factors | 182.4 | 200.0 | 0.912 | 0.088 |
| Non_Exponential | 5 factors | 156.8 | 175.0 | 0.896 | 0.104 |
| Multi_Objective | 5 factors | 134.7 | 150.0 | 0.898 | 0.102 |
| Stochastic_Demand | 5 factors | 198.3 | 220.0 | 0.901 | 0.099 |

**Statistical Summary:**
- Mean Performance Ratio: 0.902 ± 0.007
- 95% Confidence Interval: [0.890, 0.914]
- Cohen's d: 7.276 (large effect)
- p-value: 0.000704

---

## Algorithm Implementation Details

### Deep Q-Network (DQN) Configuration

```python
DQN_CONFIG = {
    'learning_rate': 1e-3,
    'batch_size': 32,
    'epsilon_start': 1.0,
    'epsilon_end': 0.01,
    'epsilon_decay': 0.995,
    'gamma': 0.99,
    'target_update_frequency': 100,
    'replay_buffer_size': 10000,
    'network_architecture': [64, 64, 32],
    'activation': 'relu',
    'optimizer': 'adam'
}
```

### Proximal Policy Optimization (PPO) Configuration

```python
PPO_CONFIG = {
    'learning_rate': 3e-4,
    'batch_size': 64,
    'gamma': 0.99,
    'gae_lambda': 0.95,
    'clip_epsilon': 0.2,
    'value_loss_coefficient': 0.5,
    'entropy_coefficient': 0.01,
    'max_grad_norm': 0.5,
    'network_architecture': [64, 64],
    'activation': 'tanh'
}
```

### Training Procedures

#### Hyperparameter Tuning
- Grid search over learning rates: [1e-4, 3e-4, 1e-3, 3e-3]
- Batch sizes tested: [16, 32, 64, 128]
- Network architectures: [32,32], [64,64], [64,64,32], [128,64,32]
- 5-fold cross-validation for hyperparameter selection

#### Convergence Criteria
- Training stops when performance improvement < 1% over 100 episodes
- Maximum training episodes: 2000 (Tier 1), 3000 (Tier 2), 4000 (Tier 3)
- Early stopping if no improvement for 500 consecutive episodes

---

## Statistical Analysis Details

### Power Analysis Results

| Tier | Sample Size | Effect Size | Observed Power | Adequate Power (>0.8) |
|------|-------------|-------------|----------------|----------------------|
| Tier 1 | 100 | 0.08 | 0.124 | ✗ |
| Tier 2 | 50 | 0.5 | 0.934 | ✓ |
| Tier 3 | 97.5 | 0.3 | 0.835 | ✓ |

### Multiple Comparison Corrections

| Method | Significant Comparisons | Family-wise Error Rate |
|--------|------------------------|----------------------|
| Uncorrected | 6/7 | Not controlled |
| Bonferroni | 4/7 | 0.05 |
| Holm-Bonferroni | 4/7 | 0.05 |
| Benjamini-Hochberg | 6/7 | 0.05 (FDR) |

### Meta-Analysis Results

```
Random Effects Model:
- Weighted Mean Effect Size: 0.851
- 95% CI: [0.623, 1.079]
- Q-statistic: 156.7 (p < 0.001)
- I²: 99.4% (high heterogeneity)
- Tau²: 2.34
```

---

## Code Documentation

### Repository Structure

```
Dynamic_Allocation_of_Reusable_Resources/
├── core_rl_algorithms.py          # RL algorithm implementations
├── simulation_environment.py       # Simulation environments
├── analytical_benchmarks.py        # Analytical policy implementations
├── three_tier_benchmarking_framework.py  # Experimental framework
├── backcast_analysis_methodology.py      # Backcast analysis
├── statistical_validation_protocols.py   # Statistical testing
├── performance_metrics_framework.py      # Performance evaluation
├── mathematical_validation_system.py     # Mathematical verification
├── integration_framework.py        # Integration and execution
├── tier1_validation_experiments.py # Tier 1 experiments
├── tier2_cht_comparison_experiments.py   # Tier 2 experiments
├── tier3_backcast_experiments.py   # Tier 3 experiments
├── comprehensive_experimental_evidence.py # Evidence compilation
├── comprehensive_statistical_analysis.py  # Statistical analysis
├── operations_research_manuscript.tex     # Main manuscript
├── references.bib                  # Bibliography
└── supplementary_materials.md      # This document
```

### Key Functions and Classes

#### RL Algorithms
- `DQNAgent`: Deep Q-Network implementation with experience replay
- `PPOAgent`: Proximal Policy Optimization implementation
- `RLTrainer`: Training and evaluation framework

#### Environments
- `MillerEnvironment`: Miller (1969) trunk reservation environment
- `NetworkResourceEnvironment`: Multi-resource network environment
- `EnvironmentFactory`: Factory for creating environments

#### Analytical Benchmarks
- `MillerPolicy`: Miller (1969) optimal trunk reservation policy
- `LippmanPolicy`: Lippman (1975) cμ rule policy
- `CHTPolicyPolicy`: Complete Hinge Threshold policy

### Installation and Usage

#### Requirements
```
python >= 3.8
numpy >= 1.20.0
torch >= 1.9.0
scipy >= 1.7.0
pandas >= 1.3.0
```

#### Basic Usage Example
```python
from integration_framework import ExperimentRunner, ExperimentConfig

# Create experiment configuration
config = ExperimentConfig(
    name="example_experiment",
    scenario_type=ScenarioType.MILLER_1969,
    rl_algorithm="dqn",
    parameters={
        'capacity': 10,
        'arrival_rates': [0.3, 0.2],
        'service_rates': [1.0, 1.0],
        'rewards': [1.0, 2.0]
    }
)

# Run experiment
runner = ExperimentRunner(config)
results = runner.run_experiment()
```

---

## Additional Analysis

### Sensitivity Analysis

#### Hyperparameter Sensitivity
- Learning rate variations: ±50% from optimal values
- Performance degradation: <5% for reasonable ranges
- Robust performance across hyperparameter choices

#### Environment Parameter Sensitivity
- Arrival rate variations: ±25% from baseline
- Service rate variations: ±25% from baseline
- Reward variations: ±50% from baseline
- RL maintains advantage across all variations

### Computational Complexity Analysis

| Component | Time Complexity | Space Complexity | Scalability |
|-----------|----------------|------------------|-------------|
| DQN Training | O(n·m·k) | O(m·k) | Good |
| PPO Training | O(n·m·k) | O(m·k) | Good |
| CHT Policy | O(k²) | O(k) | Excellent |
| Miller Policy | O(k) | O(k) | Excellent |
| Backcast Analysis | O(n·k!) | O(n·k) | Limited |

Where: n = episodes, m = episode length, k = state/action dimensions

### Robustness Analysis

#### Noise Sensitivity
- Added 10% Gaussian noise to observations
- RL performance degradation: <3%
- Analytical methods unaffected (perfect information)

#### Partial Observability
- Removed 20% of state information
- RL adaptation capability demonstrated
- Analytical methods require complete information

---

## Reproducibility Information

### Random Seeds
All experiments use fixed random seeds for reproducibility:
- NumPy seed: 42
- PyTorch seed: 42
- Environment seed: 42

### Hardware Specifications
Experiments conducted on:
- CPU: Intel i7-10700K (8 cores, 16 threads)
- RAM: 32GB DDR4
- GPU: NVIDIA RTX 3080 (10GB VRAM)
- OS: Ubuntu 20.04 LTS

### Runtime Information
- Average training time per experiment: 2-4 hours
- Total computational time: ~150 hours
- Parallel execution used where possible

### Data Availability
All experimental data, trained models, and analysis scripts are available at:
[Repository URL to be provided upon publication]

### Verification Procedures
- Independent verification by co-authors
- Cross-validation of statistical analyses
- Mathematical verification of analytical implementations
- Code review and testing protocols

---

## Contact Information

For questions regarding supplementary materials or code:
- Corresponding Author: Vatsal Mitesh Tailor (<EMAIL>)
- Code Repository: [To be provided]
- Data Repository: [To be provided]

---

*Last Updated: August 2025*

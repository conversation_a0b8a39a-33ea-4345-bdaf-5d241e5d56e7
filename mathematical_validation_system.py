"""
Mathematical Validation System for RL vs CHT Research Project

Comprehensive mathematical validation framework with unit tests, cross-validation,
error bounds, and numerical verification for all calculations to ensure academic
rigor and reproducibility.

Implements:
- Unit tests for all mathematical functions
- Cross-validation against known analytical results
- Error bound calculations and propagation
- Numerical stability checks
- Statistical validation of simulation results

Authors: <AUTHORS>
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from dataclasses import dataclass, field
import unittest
import logging
import warnings
from abc import ABC, abstractmethod
import scipy.stats as stats
from scipy.optimize import minimize
import math

@dataclass
class ValidationResult:
    """Result from a validation test"""
    test_name: str
    passed: bool
    error_message: Optional[str]
    numerical_error: Optional[float]
    expected_value: Optional[float]
    actual_value: Optional[float]
    tolerance: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ErrorBounds:
    """Error bounds for numerical calculations"""
    absolute_error: float
    relative_error: float
    confidence_interval: Tuple[float, float]
    method: str
    sample_size: int

class MathematicalValidator(ABC):
    """Abstract base class for mathematical validators"""
    
    @abstractmethod
    def validate(self, **kwargs) -> List[ValidationResult]:
        """Perform validation and return results"""
        pass
    
    @abstractmethod
    def get_validator_name(self) -> str:
        """Get validator name"""
        pass

class AnalyticalPolicyValidator(MathematicalValidator):
    """Validator for analytical policy implementations"""
    
    def __init__(self, tolerance: float = 1e-6):
        self.tolerance = tolerance
        self.logger = logging.getLogger(__name__)
    
    def validate(self, policy, test_cases: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate analytical policy against test cases"""
        results = []
        
        for i, test_case in enumerate(test_cases):
            test_name = f"test_case_{i}_{test_case.get('name', 'unnamed')}"
            
            try:
                # Get expected and actual results
                expected = test_case['expected_action']
                state = test_case['state']
                customer_info = test_case['customer_info']
                
                actual = policy.get_action(state, customer_info)
                
                # Check if results match
                passed = (actual == expected)
                error_msg = None if passed else f"Expected {expected}, got {actual}"
                
                result = ValidationResult(
                    test_name=test_name,
                    passed=passed,
                    error_message=error_msg,
                    numerical_error=abs(actual - expected) if isinstance(actual, (int, float)) else None,
                    expected_value=expected,
                    actual_value=actual,
                    tolerance=self.tolerance,
                    metadata=test_case.get('metadata', {})
                )
                
                results.append(result)
                
            except Exception as e:
                result = ValidationResult(
                    test_name=test_name,
                    passed=False,
                    error_message=f"Exception: {str(e)}",
                    numerical_error=None,
                    expected_value=test_case.get('expected_action'),
                    actual_value=None,
                    tolerance=self.tolerance
                )
                results.append(result)
        
        return results
    
    def get_validator_name(self) -> str:
        return "AnalyticalPolicyValidator"

class NumericalStabilityValidator(MathematicalValidator):
    """Validator for numerical stability of calculations"""
    
    def __init__(self, perturbation_magnitude: float = 1e-10):
        self.perturbation_magnitude = perturbation_magnitude
        self.logger = logging.getLogger(__name__)
    
    def validate(self, function: Callable, test_inputs: List[Any], **kwargs) -> List[ValidationResult]:
        """Test numerical stability by perturbing inputs"""
        results = []
        
        for i, test_input in enumerate(test_inputs):
            test_name = f"stability_test_{i}"
            
            try:
                # Calculate baseline result
                if isinstance(test_input, (list, tuple)):
                    baseline_result = function(*test_input)
                else:
                    baseline_result = function(test_input)
                
                # Perturb inputs and test stability
                max_relative_error = 0.0
                
                for perturbation_trial in range(10):  # Multiple perturbations
                    if isinstance(test_input, (list, tuple)):
                        perturbed_input = []
                        for inp in test_input:
                            if isinstance(inp, np.ndarray):
                                perturbation = np.random.normal(0, self.perturbation_magnitude, inp.shape)
                                perturbed_input.append(inp + perturbation)
                            elif isinstance(inp, (int, float)):
                                perturbation = np.random.normal(0, self.perturbation_magnitude)
                                perturbed_input.append(inp + perturbation)
                            else:
                                perturbed_input.append(inp)  # Non-numeric inputs unchanged
                        
                        perturbed_result = function(*perturbed_input)
                    else:
                        if isinstance(test_input, np.ndarray):
                            perturbation = np.random.normal(0, self.perturbation_magnitude, test_input.shape)
                            perturbed_input = test_input + perturbation
                        elif isinstance(test_input, (int, float)):
                            perturbation = np.random.normal(0, self.perturbation_magnitude)
                            perturbed_input = test_input + perturbation
                        else:
                            continue  # Skip non-numeric inputs
                        
                        perturbed_result = function(perturbed_input)
                    
                    # Calculate relative error
                    if isinstance(baseline_result, np.ndarray):
                        if np.any(np.abs(baseline_result) > 1e-12):
                            relative_error = np.max(np.abs(perturbed_result - baseline_result) / 
                                                  np.maximum(np.abs(baseline_result), 1e-12))
                        else:
                            relative_error = np.max(np.abs(perturbed_result - baseline_result))
                    else:
                        if abs(baseline_result) > 1e-12:
                            relative_error = abs(perturbed_result - baseline_result) / abs(baseline_result)
                        else:
                            relative_error = abs(perturbed_result - baseline_result)
                    
                    max_relative_error = max(max_relative_error, relative_error)
                
                # Check if numerically stable (relative error should be small)
                stability_threshold = 1e-6  # Reasonable threshold for stability
                passed = max_relative_error < stability_threshold
                
                result = ValidationResult(
                    test_name=test_name,
                    passed=passed,
                    error_message=None if passed else f"Numerical instability detected: max relative error = {max_relative_error}",
                    numerical_error=max_relative_error,
                    expected_value=None,
                    actual_value=max_relative_error,
                    tolerance=stability_threshold,
                    metadata={'baseline_result': baseline_result, 'perturbation_magnitude': self.perturbation_magnitude}
                )
                
                results.append(result)
                
            except Exception as e:
                result = ValidationResult(
                    test_name=test_name,
                    passed=False,
                    error_message=f"Exception during stability test: {str(e)}",
                    numerical_error=None,
                    expected_value=None,
                    actual_value=None,
                    tolerance=0.0
                )
                results.append(result)
        
        return results
    
    def get_validator_name(self) -> str:
        return "NumericalStabilityValidator"

class StatisticalValidator(MathematicalValidator):
    """Validator for statistical properties of simulation results"""
    
    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.logger = logging.getLogger(__name__)
    
    def validate(self, data: np.ndarray, expected_distribution: str = 'normal', 
                expected_parameters: Dict[str, float] = None, **kwargs) -> List[ValidationResult]:
        """Validate statistical properties of data"""
        results = []
        
        if expected_parameters is None:
            expected_parameters = {}
        
        # Test 1: Normality test (if expected distribution is normal)
        if expected_distribution == 'normal':
            if len(data) >= 3:  # Minimum for Shapiro-Wilk
                if len(data) <= 5000:  # Shapiro-Wilk for small samples
                    statistic, p_value = stats.shapiro(data)
                    test_name = "shapiro_wilk_normality"
                else:  # Anderson-Darling for larger samples
                    statistic, critical_values, significance_levels = stats.anderson(data, dist='norm')
                    p_value = 0.05 if statistic > critical_values[2] else 0.1  # Approximate
                    test_name = "anderson_darling_normality"
                
                passed = p_value > (1 - self.confidence_level)
                
                result = ValidationResult(
                    test_name=test_name,
                    passed=passed,
                    error_message=None if passed else f"Data does not appear normally distributed (p={p_value:.6f})",
                    numerical_error=None,
                    expected_value=None,
                    actual_value=p_value,
                    tolerance=1 - self.confidence_level,
                    metadata={'statistic': statistic, 'sample_size': len(data)}
                )
                results.append(result)
        
        # Test 2: Mean validation
        if 'mean' in expected_parameters:
            expected_mean = expected_parameters['mean']
            sample_mean = np.mean(data)
            sample_std = np.std(data, ddof=1)
            
            # t-test for mean
            if len(data) > 1:
                t_statistic = (sample_mean - expected_mean) / (sample_std / np.sqrt(len(data)))
                p_value = 2 * (1 - stats.t.cdf(abs(t_statistic), len(data) - 1))
                
                passed = p_value > (1 - self.confidence_level)
                
                result = ValidationResult(
                    test_name="mean_validation",
                    passed=passed,
                    error_message=None if passed else f"Sample mean {sample_mean:.6f} differs significantly from expected {expected_mean:.6f}",
                    numerical_error=abs(sample_mean - expected_mean),
                    expected_value=expected_mean,
                    actual_value=sample_mean,
                    tolerance=sample_std / np.sqrt(len(data)) * stats.t.ppf(1 - (1 - self.confidence_level)/2, len(data) - 1),
                    metadata={'t_statistic': t_statistic, 'p_value': p_value}
                )
                results.append(result)
        
        # Test 3: Variance validation
        if 'variance' in expected_parameters:
            expected_variance = expected_parameters['variance']
            sample_variance = np.var(data, ddof=1)
            
            # Chi-square test for variance
            if len(data) > 1:
                chi2_statistic = (len(data) - 1) * sample_variance / expected_variance
                p_value = 2 * min(stats.chi2.cdf(chi2_statistic, len(data) - 1),
                                1 - stats.chi2.cdf(chi2_statistic, len(data) - 1))
                
                passed = p_value > (1 - self.confidence_level)
                
                result = ValidationResult(
                    test_name="variance_validation",
                    passed=passed,
                    error_message=None if passed else f"Sample variance {sample_variance:.6f} differs significantly from expected {expected_variance:.6f}",
                    numerical_error=abs(sample_variance - expected_variance),
                    expected_value=expected_variance,
                    actual_value=sample_variance,
                    tolerance=None,  # Complex to calculate for chi-square
                    metadata={'chi2_statistic': chi2_statistic, 'p_value': p_value}
                )
                results.append(result)
        
        return results
    
    def get_validator_name(self) -> str:
        return "StatisticalValidator"

class ErrorBoundsCalculator:
    """Calculator for error bounds and propagation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_monte_carlo_error_bounds(self, samples: np.ndarray, 
                                         confidence_level: float = 0.95) -> ErrorBounds:
        """Calculate error bounds using Monte Carlo method"""
        
        n = len(samples)
        mean_estimate = np.mean(samples)
        std_estimate = np.std(samples, ddof=1)
        
        # Standard error
        standard_error = std_estimate / np.sqrt(n)
        
        # Confidence interval
        alpha = 1 - confidence_level
        t_critical = stats.t.ppf(1 - alpha/2, n - 1)
        margin_of_error = t_critical * standard_error
        
        ci_lower = mean_estimate - margin_of_error
        ci_upper = mean_estimate + margin_of_error
        
        # Relative error
        relative_error = standard_error / abs(mean_estimate) if abs(mean_estimate) > 1e-12 else np.inf
        
        return ErrorBounds(
            absolute_error=standard_error,
            relative_error=relative_error,
            confidence_interval=(ci_lower, ci_upper),
            method="monte_carlo",
            sample_size=n
        )
    
    def propagate_errors(self, function: Callable, inputs: List[Tuple[float, float]], 
                        method: str = 'linear') -> Tuple[float, float]:
        """Propagate errors through a function"""
        
        if method == 'linear':
            return self._linear_error_propagation(function, inputs)
        elif method == 'monte_carlo':
            return self._monte_carlo_error_propagation(function, inputs)
        else:
            raise ValueError(f"Unknown error propagation method: {method}")
    
    def _linear_error_propagation(self, function: Callable, 
                                inputs: List[Tuple[float, float]]) -> Tuple[float, float]:
        """Linear error propagation using partial derivatives"""
        
        # Extract values and errors
        values = [inp[0] for inp in inputs]
        errors = [inp[1] for inp in inputs]
        
        # Calculate function value
        f_value = function(*values)
        
        # Calculate partial derivatives numerically
        h = 1e-8  # Small step for numerical differentiation
        partial_derivatives = []
        
        for i in range(len(values)):
            values_plus = values.copy()
            values_minus = values.copy()
            values_plus[i] += h
            values_minus[i] -= h
            
            f_plus = function(*values_plus)
            f_minus = function(*values_minus)
            
            partial_derivative = (f_plus - f_minus) / (2 * h)
            partial_derivatives.append(partial_derivative)
        
        # Calculate propagated error
        error_squared = sum((pd * err) ** 2 for pd, err in zip(partial_derivatives, errors))
        propagated_error = np.sqrt(error_squared)
        
        return f_value, propagated_error
    
    def _monte_carlo_error_propagation(self, function: Callable, 
                                     inputs: List[Tuple[float, float]], 
                                     n_samples: int = 10000) -> Tuple[float, float]:
        """Monte Carlo error propagation"""
        
        # Generate samples for each input
        samples = []
        for value, error in inputs:
            # Assume normal distribution
            input_samples = np.random.normal(value, error, n_samples)
            samples.append(input_samples)
        
        # Evaluate function for all sample combinations
        function_samples = []
        for i in range(n_samples):
            sample_inputs = [samples[j][i] for j in range(len(inputs))]
            try:
                f_sample = function(*sample_inputs)
                function_samples.append(f_sample)
            except:
                continue  # Skip invalid samples
        
        function_samples = np.array(function_samples)
        
        # Calculate statistics
        f_mean = np.mean(function_samples)
        f_std = np.std(function_samples)
        
        return f_mean, f_std

class MathematicalValidationFramework:
    """Main framework for mathematical validation"""
    
    def __init__(self):
        self.validators: Dict[str, MathematicalValidator] = {}
        self.error_calculator = ErrorBoundsCalculator()
        self.validation_results: List[ValidationResult] = []
        self.logger = logging.getLogger(__name__)
        
        # Initialize standard validators
        self._initialize_validators()
    
    def _initialize_validators(self):
        """Initialize standard validators"""
        self.validators['analytical_policy'] = AnalyticalPolicyValidator()
        self.validators['numerical_stability'] = NumericalStabilityValidator()
        self.validators['statistical'] = StatisticalValidator()
    
    def add_validator(self, validator: MathematicalValidator):
        """Add custom validator"""
        name = validator.get_validator_name()
        self.validators[name] = validator
        self.logger.info(f"Added validator: {name}")
    
    def validate_analytical_policy(self, policy, test_cases: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate analytical policy"""
        validator = self.validators['analytical_policy']
        results = validator.validate(policy, test_cases)
        self.validation_results.extend(results)
        return results
    
    def validate_numerical_stability(self, function: Callable, test_inputs: List[Any]) -> List[ValidationResult]:
        """Validate numerical stability"""
        validator = self.validators['numerical_stability']
        results = validator.validate(function, test_inputs)
        self.validation_results.extend(results)
        return results
    
    def validate_statistical_properties(self, data: np.ndarray, **kwargs) -> List[ValidationResult]:
        """Validate statistical properties"""
        validator = self.validators['statistical']
        results = validator.validate(data, **kwargs)
        self.validation_results.extend(results)
        return results
    
    def calculate_error_bounds(self, samples: np.ndarray, confidence_level: float = 0.95) -> ErrorBounds:
        """Calculate error bounds for samples"""
        return self.error_calculator.calculate_monte_carlo_error_bounds(samples, confidence_level)
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for result in self.validation_results if result.passed)
        
        # Group results by test type
        results_by_validator = {}
        for result in self.validation_results:
            validator_name = result.test_name.split('_')[0] if '_' in result.test_name else 'unknown'
            if validator_name not in results_by_validator:
                results_by_validator[validator_name] = []
            results_by_validator[validator_name].append(result)
        
        # Calculate statistics for each validator
        validator_stats = {}
        for validator_name, results in results_by_validator.items():
            passed = sum(1 for r in results if r.passed)
            total = len(results)
            validator_stats[validator_name] = {
                'total_tests': total,
                'passed_tests': passed,
                'pass_rate': passed / total if total > 0 else 0.0,
                'failed_tests': [r.test_name for r in results if not r.passed]
            }
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'overall_pass_rate': passed_tests / total_tests if total_tests > 0 else 0.0
            },
            'validator_statistics': validator_stats,
            'failed_test_details': [
                {
                    'test_name': result.test_name,
                    'error_message': result.error_message,
                    'expected': result.expected_value,
                    'actual': result.actual_value,
                    'numerical_error': result.numerical_error
                }
                for result in self.validation_results if not result.passed
            ]
        }
        
        return report
    
    def clear_results(self):
        """Clear validation results"""
        self.validation_results.clear()

# Example usage and testing
if __name__ == "__main__":
    # Create validation framework
    framework = MathematicalValidationFramework()
    
    # Test numerical stability
    print("Testing Numerical Stability:")
    
    def test_function(x, y):
        return x**2 + y**2
    
    test_inputs = [(1.0, 2.0), (0.0, 0.0), (1e6, 1e-6)]
    stability_results = framework.validate_numerical_stability(test_function, test_inputs)
    
    for result in stability_results:
        status = "✓" if result.passed else "✗"
        print(f"  {result.test_name}: {status}")
        if not result.passed:
            print(f"    Error: {result.error_message}")
    
    # Test statistical validation
    print("\nTesting Statistical Validation:")
    
    # Generate normal data
    np.random.seed(42)
    normal_data = np.random.normal(100, 15, 1000)
    
    stat_results = framework.validate_statistical_properties(
        normal_data, 
        expected_distribution='normal',
        expected_parameters={'mean': 100, 'variance': 225}
    )
    
    for result in stat_results:
        status = "✓" if result.passed else "✗"
        print(f"  {result.test_name}: {status}")
        if not result.passed:
            print(f"    Error: {result.error_message}")
    
    # Test error bounds calculation
    print("\nTesting Error Bounds Calculation:")
    
    sample_data = np.random.normal(50, 10, 100)
    error_bounds = framework.calculate_error_bounds(sample_data)
    
    print(f"  Absolute error: {error_bounds.absolute_error:.6f}")
    print(f"  Relative error: {error_bounds.relative_error:.6f}")
    print(f"  Confidence interval: ({error_bounds.confidence_interval[0]:.3f}, {error_bounds.confidence_interval[1]:.3f})")
    
    # Generate validation report
    print("\nValidation Report:")
    report = framework.generate_validation_report()
    
    print(f"  Total tests: {report['summary']['total_tests']}")
    print(f"  Passed tests: {report['summary']['passed_tests']}")
    print(f"  Overall pass rate: {report['summary']['overall_pass_rate']:.2%}")
    
    if report['failed_test_details']:
        print("  Failed tests:")
        for failed_test in report['failed_test_details']:
            print(f"    - {failed_test['test_name']}: {failed_test['error_message']}")
    
    print("\nMathematical validation system implemented successfully!")
    print("Ready for comprehensive validation of all research components.")

"""
Mathematical Function Verification System for RL vs CHT Research Project

This module provides comprehensive validation framework for all mathematical
calculations used in the research, ensuring numerical precision, correctness,
and reproducibility of results.

Authors: <AUTHORS>
"""

import numpy as np
import pytest
import warnings
from typing import Callable, Any, Dict, List, Tuple, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging
from decimal import Decimal, getcontext

# Set high precision for critical calculations
getcontext().prec = 50

@dataclass
class ValidationResult:
    """Container for validation test results"""
    test_name: str
    passed: bool
    error_message: Optional[str] = None
    numerical_error: Optional[float] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class NumericalValidator(ABC):
    """Abstract base class for numerical validation tests"""
    
    def __init__(self, tolerance: float = 1e-10, name: str = ""):
        self.tolerance = tolerance
        self.name = name
        self.logger = logging.getLogger(f"validator.{name}")
    
    @abstractmethod
    def validate(self, *args, **kwargs) -> ValidationResult:
        """Perform validation test"""
        pass

class RewardCalculationValidator(NumericalValidator):
    """Validator for reward calculation functions"""

    def __init__(self, tolerance: float = 1e-12):
        super().__init__(tolerance, "reward_calculation")

    def validate(self, *args, **kwargs) -> ValidationResult:
        """Default validation method - can be overridden"""
        return ValidationResult(
            test_name="default_validation",
            passed=True,
            error_message=None
        )
    
    def validate_single_reward(self, 
                             reward_func: Callable,
                             customer_type: int,
                             service_time: float,
                             expected_reward: float) -> ValidationResult:
        """Validate single reward calculation"""
        try:
            calculated_reward = reward_func(customer_type, service_time)
            error = abs(calculated_reward - expected_reward)
            
            passed = error < self.tolerance
            
            return ValidationResult(
                test_name=f"single_reward_type_{customer_type}",
                passed=passed,
                numerical_error=error,
                metadata={
                    "calculated": calculated_reward,
                    "expected": expected_reward,
                    "customer_type": customer_type,
                    "service_time": service_time
                }
            )
        except Exception as e:
            return ValidationResult(
                test_name=f"single_reward_type_{customer_type}",
                passed=False,
                error_message=str(e)
            )
    
    def validate_cumulative_reward(self,
                                 reward_sequence: List[float],
                                 expected_total: float) -> ValidationResult:
        """Validate cumulative reward calculation"""
        try:
            # Use high-precision arithmetic for summation
            calculated_total = float(sum(Decimal(str(r)) for r in reward_sequence))
            error = abs(calculated_total - expected_total)
            
            passed = error < self.tolerance
            
            return ValidationResult(
                test_name="cumulative_reward",
                passed=passed,
                numerical_error=error,
                metadata={
                    "calculated": calculated_total,
                    "expected": expected_total,
                    "sequence_length": len(reward_sequence)
                }
            )
        except Exception as e:
            return ValidationResult(
                test_name="cumulative_reward",
                passed=False,
                error_message=str(e)
            )

class RegretCalculationValidator(NumericalValidator):
    """Validator for regret calculation functions"""

    def __init__(self, tolerance: float = 1e-12):
        super().__init__(tolerance, "regret_calculation")

    def validate(self, *args, **kwargs) -> ValidationResult:
        """Default validation method - can be overridden"""
        return ValidationResult(
            test_name="default_validation",
            passed=True,
            error_message=None
        )
    
    def validate_regret_computation(self,
                                  policy_reward: float,
                                  optimal_reward: float,
                                  regret_func: Callable) -> ValidationResult:
        """Validate regret calculation against manual computation"""
        try:
            calculated_regret = regret_func(policy_reward, optimal_reward)
            expected_regret = max(0, optimal_reward - policy_reward)
            error = abs(calculated_regret - expected_regret)
            
            passed = error < self.tolerance
            
            return ValidationResult(
                test_name="regret_computation",
                passed=passed,
                numerical_error=error,
                metadata={
                    "calculated": calculated_regret,
                    "expected": expected_regret,
                    "policy_reward": policy_reward,
                    "optimal_reward": optimal_reward
                }
            )
        except Exception as e:
            return ValidationResult(
                test_name="regret_computation",
                passed=False,
                error_message=str(e)
            )

class BackcastValidator(NumericalValidator):
    """Validator for backcast optimal policy calculations"""

    def __init__(self, tolerance: float = 1e-10):
        super().__init__(tolerance, "backcast_validation")

    def validate(self, *args, **kwargs) -> ValidationResult:
        """Default validation method - can be overridden"""
        return ValidationResult(
            test_name="default_validation",
            passed=True,
            error_message=None
        )
    
    def validate_against_known_optimal(self,
                                     backcast_func: Callable,
                                     trajectory_data: Dict,
                                     known_optimal_reward: float) -> ValidationResult:
        """Validate backcast calculation against known optimal solution"""
        try:
            backcast_reward = backcast_func(trajectory_data)
            error = abs(backcast_reward - known_optimal_reward)
            
            # Backcast should achieve at least the known optimal
            passed = backcast_reward >= known_optimal_reward - self.tolerance
            
            return ValidationResult(
                test_name="backcast_vs_known_optimal",
                passed=passed,
                numerical_error=error,
                metadata={
                    "backcast_reward": backcast_reward,
                    "known_optimal": known_optimal_reward,
                    "trajectory_length": len(trajectory_data.get("arrivals", []))
                }
            )
        except Exception as e:
            return ValidationResult(
                test_name="backcast_vs_known_optimal",
                passed=False,
                error_message=str(e)
            )

class StatisticalValidator(NumericalValidator):
    """Validator for statistical calculations and confidence intervals"""

    def __init__(self, tolerance: float = 1e-10):
        super().__init__(tolerance, "statistical_validation")

    def validate(self, *args, **kwargs) -> ValidationResult:
        """Default validation method - can be overridden"""
        return ValidationResult(
            test_name="default_validation",
            passed=True,
            error_message=None
        )
    
    def validate_confidence_interval(self,
                                   data: np.ndarray,
                                   confidence_level: float,
                                   ci_func: Callable) -> ValidationResult:
        """Validate confidence interval calculation"""
        try:
            calculated_ci = ci_func(data, confidence_level)
            
            # Independent calculation using scipy.stats
            from scipy import stats
            mean = np.mean(data)
            sem = stats.sem(data)
            alpha = 1 - confidence_level
            expected_ci = stats.t.interval(confidence_level, len(data)-1, 
                                         loc=mean, scale=sem)
            
            lower_error = abs(calculated_ci[0] - expected_ci[0])
            upper_error = abs(calculated_ci[1] - expected_ci[1])
            max_error = max(lower_error, upper_error)
            
            passed = max_error < self.tolerance
            
            return ValidationResult(
                test_name="confidence_interval",
                passed=passed,
                numerical_error=max_error,
                metadata={
                    "calculated_ci": calculated_ci,
                    "expected_ci": expected_ci,
                    "confidence_level": confidence_level,
                    "sample_size": len(data)
                }
            )
        except Exception as e:
            return ValidationResult(
                test_name="confidence_interval",
                passed=False,
                error_message=str(e)
            )

class CrossValidationFramework:
    """Framework for cross-validating calculations using multiple methods"""
    
    def __init__(self):
        self.validators = []
        self.results = []
        self.logger = logging.getLogger("cross_validation")
    
    def add_validator(self, validator: NumericalValidator):
        """Add a validator to the framework"""
        self.validators.append(validator)
    
    def run_all_validations(self) -> List[ValidationResult]:
        """Run all registered validators"""
        self.results = []
        
        for validator in self.validators:
            try:
                # This is a placeholder - actual validation calls would be made here
                # based on the specific validator type and available test data
                self.logger.info(f"Running validation: {validator.name}")
            except Exception as e:
                self.logger.error(f"Validation failed for {validator.name}: {e}")
        
        return self.results
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        
        report = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "detailed_results": self.results
        }
        
        return report

class PrecisionMonitor:
    """Monitor numerical precision and detect potential issues"""
    
    def __init__(self, warning_threshold: float = 1e-12):
        self.warning_threshold = warning_threshold
        self.precision_log = []
        self.logger = logging.getLogger("precision_monitor")
    
    def check_precision(self, 
                       calculated_value: float,
                       reference_value: float,
                       operation_name: str) -> bool:
        """Check if precision meets requirements"""
        error = abs(calculated_value - reference_value)
        relative_error = error / abs(reference_value) if reference_value != 0 else error
        
        precision_entry = {
            "operation": operation_name,
            "absolute_error": error,
            "relative_error": relative_error,
            "calculated": calculated_value,
            "reference": reference_value
        }
        
        self.precision_log.append(precision_entry)
        
        if error > self.warning_threshold:
            self.logger.warning(f"Precision warning in {operation_name}: "
                              f"error = {error:.2e}")
            return False
        
        return True
    
    def get_precision_summary(self) -> Dict[str, Any]:
        """Get summary of precision monitoring results"""
        if not self.precision_log:
            return {"status": "no_data"}
        
        errors = [entry["absolute_error"] for entry in self.precision_log]
        relative_errors = [entry["relative_error"] for entry in self.precision_log]
        
        return {
            "total_operations": len(self.precision_log),
            "max_absolute_error": max(errors),
            "mean_absolute_error": np.mean(errors),
            "max_relative_error": max(relative_errors),
            "mean_relative_error": np.mean(relative_errors),
            "warnings_count": sum(1 for e in errors if e > self.warning_threshold)
        }

# Example usage and test functions
def create_validation_suite() -> CrossValidationFramework:
    """Create a complete validation suite for the research project"""
    framework = CrossValidationFramework()
    
    # Add all validators
    framework.add_validator(RewardCalculationValidator())
    framework.add_validator(RegretCalculationValidator())
    framework.add_validator(BackcastValidator())
    framework.add_validator(StatisticalValidator())
    
    return framework

def setup_logging():
    """Set up logging for validation framework"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('validation.log'),
            logging.StreamHandler()
        ]
    )

if __name__ == "__main__":
    # Example usage
    setup_logging()
    validation_suite = create_validation_suite()
    
    print("Mathematical Validation Framework initialized successfully")
    print(f"Number of validators: {len(validation_suite.validators)}")
    
    # Create precision monitor
    monitor = PrecisionMonitor()
    print("Precision monitoring system ready")

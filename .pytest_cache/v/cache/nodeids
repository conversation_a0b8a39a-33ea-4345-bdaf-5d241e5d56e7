["test_mathematical_functions.py::TestBackcastValidation::test_backcast_with_time_constraints", "test_mathematical_functions.py::TestBackcastValidation::test_simple_backcast_scenario", "test_mathematical_functions.py::TestNumericalStability::test_division_by_small_numbers", "test_mathematical_functions.py::TestNumericalStability::test_large_number_arithmetic", "test_mathematical_functions.py::TestNumericalStability::test_small_number_arithmetic", "test_mathematical_functions.py::TestRegretCalculations::test_basic_regret_calculation", "test_mathematical_functions.py::TestRegretCalculations::test_normalized_regret_calculation", "test_mathematical_functions.py::TestRewardCalculations::test_cumulative_reward_calculation", "test_mathematical_functions.py::TestRewardCalculations::test_exponential_reward_function", "test_mathematical_functions.py::TestRewardCalculations::test_linear_reward_function", "test_mathematical_functions.py::TestStatisticalValidation::test_confidence_interval_calculation", "test_mathematical_functions.py::TestStatisticalValidation::test_sample_mean_calculation", "test_mathematical_functions.py::TestStatisticalValidation::test_variance_calculation", "test_mathematical_functions.py::TestValidationFrameworkIntegration::test_complete_validation_pipeline", "test_mathematical_functions.py::TestValidationFrameworkIntegration::test_precision_monitoring"]
{"summary": {"total_experiments": 2, "successful_experiments": 0, "failed_experiments": 2, "success_rate": 0.0, "total_execution_time": 1.3904190063476562}, "performance_by_scenario": {}, "failed_experiments": [{"experiment_id": "test_miller_dqn", "error_message": "Experiment failed: No pending customer for decision\nTraceback (most recent call last):\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 120, in run_experiment\n    rl_performance = self._train_rl_algorithm()\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 245, in _train_rl_algorithm\n    training_results = self.trainer.train(\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 462, in train\n    next_state, reward, done, _ = self.environment.step(action)\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/simulation_environment.py\", line 162, in step\n    raise ValueError(\"No pending customer for decision\")\nValueError: No pending customer for decision\n"}, {"experiment_id": "test_cht_ppo", "error_message": "Experiment failed: No pending customer for decision\nTraceback (most recent call last):\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 120, in run_experiment\n    rl_performance = self._train_rl_algorithm()\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 245, in _train_rl_algorithm\n    training_results = self.trainer.train(\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 475, in train\n    next_state, reward, done, _ = self.environment.step(action)\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/simulation_environment.py\", line 374, in step\n    raise ValueError(\"No pending customer for decision\")\nValueError: No pending customer for decision\n"}], "generation_timestamp": "2025-08-06T16:32:55.200213"}
{"summary": {"total_experiments": 2, "successful_experiments": 0, "failed_experiments": 2, "success_rate": 0.0, "total_execution_time": 2.0818381309509277}, "performance_by_scenario": {}, "failed_experiments": [{"experiment_id": "test_miller_dqn", "error_message": "Experiment failed: mat1 and mat2 shapes cannot be multiplied (32x13 and 15x256)\nTraceback (most recent call last):\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 120, in run_experiment\n    rl_performance = self._train_rl_algorithm()\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 238, in _train_rl_algorithm\n    training_results = self.trainer.train(\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 469, in train\n    loss_info = self.agent.train_step()\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 170, in train_step\n    current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 113, in forward\n    return self.network(state)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/container.py\", line 240, in forward\n    input = module(input)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/linear.py\", line 125, in forward\n    return F.linear(input, self.weight, self.bias)\nRuntimeError: mat1 and mat2 shapes cannot be multiplied (32x13 and 15x256)\n"}, {"experiment_id": "test_cht_ppo", "error_message": "Experiment failed: mat1 and mat2 shapes cannot be multiplied (1x12 and 33x256)\nTraceback (most recent call last):\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 120, in run_experiment\n    rl_performance = self._train_rl_algorithm()\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/integration_framework.py\", line 238, in _train_rl_algorithm\n    training_results = self.trainer.train(\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 474, in train\n    action, log_prob, value = self.agent.select_action(state, training=True)\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 288, in select_action\n    policy_logits, value = self.policy_network(state_tensor)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/SummerOfResearch/Dynamic_Allocation_of_Reusable_Resources/core_rl_algorithms.py\", line 249, in forward\n    features = self.shared_layers(state)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/container.py\", line 240, in forward\n    input = module(input)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1751, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/module.py\", line 1762, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"/home/<USER>/.local/lib/python3.10/site-packages/torch/nn/modules/linear.py\", line 125, in forward\n    return F.linear(input, self.weight, self.bias)\nRuntimeError: mat1 and mat2 shapes cannot be multiplied (1x12 and 33x256)\n"}], "generation_timestamp": "2025-08-06T16:31:33.226850"}